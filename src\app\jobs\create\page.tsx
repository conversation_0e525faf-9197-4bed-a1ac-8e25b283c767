'use client';

import React, { useState } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { RoleGuard } from '@/components/auth/RoleGuard';
import {
  Briefcase,
  MapPin,
  DollarSign,
  Calendar,
  Plus,
  X,
  Save,
  Eye,
  ArrowLeft
} from 'lucide-react';
import Link from 'next/link';
import toast from 'react-hot-toast';

interface JobFormData {
  title: string;
  description: string;
  requirements: string[];
  responsibilities: string[];
  type: 'full-time' | 'part-time' | 'contract' | 'internship' | 'apprenticeship';
  location: {
    type: 'remote' | 'on-site' | 'hybrid';
    city: string;
    country: string;
    address: string;
  };
  salary: {
    min: number | '';
    max: number | '';
    currency: string;
    period: 'hourly' | 'monthly' | 'yearly';
  };
  category: string;
  skillsRequired: string[];
  experienceLevel: 'entry' | 'mid' | 'senior' | 'executive';
  benefits: string[];
  applicationDeadline: string;
  startDate: string;
}

const jobCategories = [
  'Space Technology',
  'Satellite Engineering',
  'AI & Machine Learning',
  'Cybersecurity',
  'Data Science',
  'Software Engineering',
  'Robotics',
  'Research & Development',
  'Business & Management',
  'Marketing & Communications',
  'Other'
];

const countries = [
  'Kenya', 'Ghana', 'Egypt', 'South Africa', 'Nigeria', 'Morocco', 'Tunisia',
  'Ethiopia', 'Rwanda', 'Uganda', 'Tanzania', 'Botswana', 'Namibia', 'Other'
];

export default function CreateJobPage() {
  const { data: session } = useSession();
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [currentStep, setCurrentStep] = useState(1);
  const totalSteps = 4;

  const [formData, setFormData] = useState<JobFormData>({
    title: '',
    description: '',
    requirements: [''],
    responsibilities: [''],
    type: 'full-time',
    location: {
      type: 'on-site',
      city: '',
      country: '',
      address: ''
    },
    salary: {
      min: '',
      max: '',
      currency: 'USD',
      period: 'yearly'
    },
    category: '',
    skillsRequired: [''],
    experienceLevel: 'mid',
    benefits: [''],
    applicationDeadline: '',
    startDate: ''
  });

  const updateFormData = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const updateNestedFormData = (parent: string, field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [parent]: {
        ...prev[parent as keyof JobFormData],
        [field]: value
      }
    }));
  };

  const addArrayItem = (field: keyof JobFormData) => {
    setFormData(prev => ({
      ...prev,
      [field]: [...(prev[field] as string[]), '']
    }));
  };

  const removeArrayItem = (field: keyof JobFormData, index: number) => {
    setFormData(prev => ({
      ...prev,
      [field]: (prev[field] as string[]).filter((_, i) => i !== index)
    }));
  };

  const updateArrayItem = (field: keyof JobFormData, index: number, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: (prev[field] as string[]).map((item, i) => i === index ? value : item)
    }));
  };

  const validateStep = (step: number): boolean => {
    switch (step) {
      case 1:
        return !!(formData.title && formData.description && formData.category);
      case 2:
        return !!(formData.type && formData.location.type &&
                 (formData.location.type === 'remote' || (formData.location.city && formData.location.country)));
      case 3:
        return formData.requirements.some(req => req.trim()) &&
               formData.skillsRequired.some(skill => skill.trim());
      case 4:
        return true; // Optional fields
      default:
        return false;
    }
  };

  const nextStep = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => Math.min(prev + 1, totalSteps));
    } else {
      toast.error('Please fill in all required fields before proceeding');
    }
  };

  const prevStep = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1));
  };

  const handleSubmit = async (isDraft = false) => {
    if (!session?.user?.id) {
      toast.error('You must be logged in to create a job posting');
      return;
    }

    if (!isDraft && !validateStep(1)) {
      toast.error('Please fill in all required fields');
      return;
    }

    setIsSubmitting(true);

    try {
      // Clean up the data
      const cleanedData = {
        ...formData,
        employerId: session.user.id,
        company: session.user.name || 'Company Name', // This should come from company profile
        requirements: formData.requirements.filter(req => req.trim()),
        responsibilities: formData.responsibilities.filter(resp => resp.trim()),
        skillsRequired: formData.skillsRequired.filter(skill => skill.trim()),
        benefits: formData.benefits.filter(benefit => benefit.trim()),
        salary: {
          ...formData.salary,
          min: formData.salary.min ? Number(formData.salary.min) : undefined,
          max: formData.salary.max ? Number(formData.salary.max) : undefined,
        },
        applicationDeadline: formData.applicationDeadline ? new Date(formData.applicationDeadline) : undefined,
        startDate: formData.startDate ? new Date(formData.startDate) : undefined,
        isActive: !isDraft
      };

      const response = await fetch('/api/jobs', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(cleanedData),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to create job posting');
      }

      toast.success(isDraft ? 'Job saved as draft' : 'Job posted successfully!');
      router.push('/dashboard/employer/jobs');
    } catch (error) {
      console.error('Error creating job:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to create job posting');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <RoleGuard allowedRoles={['organisation']}>
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white shadow-sm border-b">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between h-16">
              <div className="flex items-center space-x-4">
                <Link
                  href="/dashboard/organisation/jobs"
                  className="flex items-center text-gray-600 hover:text-gray-900"
                >
                  <ArrowLeft className="h-5 w-5 mr-2" />
                  Back to Jobs
                </Link>
                <div className="h-6 w-px bg-gray-300" />
                <div>
                  <h1 className="text-xl font-semibold text-gray-900">Create Job Posting</h1>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <button
                  onClick={() => handleSubmit(true)}
                  disabled={isSubmitting}
                  className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors disabled:opacity-50"
                >
                  Save Draft
                </button>
                <button
                  onClick={() => handleSubmit(false)}
                  disabled={isSubmitting || !validateStep(1)}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50 flex items-center"
                >
                  <Briefcase className="h-4 w-4 mr-2" />
                  {isSubmitting ? 'Publishing...' : 'Publish Job'}
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="bg-white border-b">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="py-4">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-gray-700">
                  Step {currentStep} of {totalSteps}
                </span>
                <span className="text-sm text-gray-500">
                  {Math.round((currentStep / totalSteps) * 100)}% Complete
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${(currentStep / totalSteps) * 100}%` }}
                />
              </div>
              <div className="flex justify-between mt-2 text-xs text-gray-500">
                <span>Basic Info</span>
                <span>Job Details</span>
                <span>Requirements</span>
                <span>Additional Info</span>
              </div>
            </div>
          </div>
        </div>

        {/* Form Content */}
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="bg-white rounded-lg shadow">
            <div className="p-6">
              {/* Step 1: Basic Information */}
              {currentStep === 1 && (
                <div className="space-y-6">
                  <div>
                    <h2 className="text-lg font-semibold text-gray-900 mb-4">Basic Information</h2>
                    <p className="text-gray-600 mb-6">
                      Start with the essential details about your job posting.
                    </p>
                  </div>

                  {/* Job Title */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Job Title *
                    </label>
                    <input
                      type="text"
                      value={formData.title}
                      onChange={(e) => updateFormData('title', e.target.value)}
                      placeholder="e.g. Senior Satellite Systems Engineer"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      required
                    />
                  </div>

                  {/* Category */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Job Category *
                    </label>
                    <select
                      value={formData.category}
                      onChange={(e) => updateFormData('category', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      required
                    >
                      <option value="">Select a category</option>
                      {jobCategories.map(category => (
                        <option key={category} value={category}>{category}</option>
                      ))}
                    </select>
                  </div>

                  {/* Job Description */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Job Description *
                    </label>
                    <textarea
                      value={formData.description}
                      onChange={(e) => updateFormData('description', e.target.value)}
                      placeholder="Describe the role, what the candidate will be doing, and what makes this opportunity exciting..."
                      rows={6}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      required
                    />
                    <p className="text-sm text-gray-500 mt-1">
                      Provide a detailed description of the role and responsibilities.
                    </p>
                  </div>
                </div>
              )}

              {/* Step 2: Job Details */}
              {currentStep === 2 && (
                <div className="space-y-6">
                  <div>
                    <h2 className="text-lg font-semibold text-gray-900 mb-4">Job Details</h2>
                    <p className="text-gray-600 mb-6">
                      Specify the employment type, location, and compensation details.
                    </p>
                  </div>

                  {/* Employment Type */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Employment Type *
                    </label>
                    <select
                      value={formData.type}
                      onChange={(e) => updateFormData('type', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      required
                    >
                      <option value="full-time">Full-time</option>
                      <option value="part-time">Part-time</option>
                      <option value="contract">Contract</option>
                      <option value="internship">Internship</option>
                      <option value="apprenticeship">Apprenticeship</option>
                    </select>
                  </div>

                  {/* Experience Level */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Experience Level *
                    </label>
                    <select
                      value={formData.experienceLevel}
                      onChange={(e) => updateFormData('experienceLevel', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      required
                    >
                      <option value="entry">Entry Level (0-2 years)</option>
                      <option value="mid">Mid Level (2-5 years)</option>
                      <option value="senior">Senior Level (5+ years)</option>
                      <option value="executive">Executive Level</option>
                    </select>
                  </div>

                  {/* Location */}
                  <div className="space-y-4">
                    <label className="block text-sm font-medium text-gray-700">
                      Location *
                    </label>

                    {/* Location Type */}
                    <div>
                      <label className="block text-sm font-medium text-gray-600 mb-2">
                        Work Arrangement
                      </label>
                      <div className="grid grid-cols-3 gap-3">
                        {['remote', 'on-site', 'hybrid'].map((type) => (
                          <label key={type} className="flex items-center">
                            <input
                              type="radio"
                              name="locationType"
                              value={type}
                              checked={formData.location.type === type}
                              onChange={(e) => updateNestedFormData('location', 'type', e.target.value)}
                              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                            />
                            <span className="ml-2 text-sm text-gray-700 capitalize">{type}</span>
                          </label>
                        ))}
                      </div>
                    </div>

                    {/* City and Country (if not remote) */}
                    {formData.location.type !== 'remote' && (
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-600 mb-2">
                            City *
                          </label>
                          <input
                            type="text"
                            value={formData.location.city}
                            onChange={(e) => updateNestedFormData('location', 'city', e.target.value)}
                            placeholder="e.g. Nairobi"
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            required
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-600 mb-2">
                            Country *
                          </label>
                          <select
                            value={formData.location.country}
                            onChange={(e) => updateNestedFormData('location', 'country', e.target.value)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            required
                          >
                            <option value="">Select country</option>
                            {countries.map(country => (
                              <option key={country} value={country}>{country}</option>
                            ))}
                          </select>
                        </div>
                      </div>
                    )}

                    {/* Address (optional) */}
                    {formData.location.type !== 'remote' && (
                      <div>
                        <label className="block text-sm font-medium text-gray-600 mb-2">
                          Address (Optional)
                        </label>
                        <input
                          type="text"
                          value={formData.location.address}
                          onChange={(e) => updateNestedFormData('location', 'address', e.target.value)}
                          placeholder="Full address or area"
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        />
                      </div>
                    )}
                  </div>

                  {/* Salary */}
                  <div className="space-y-4">
                    <label className="block text-sm font-medium text-gray-700">
                      Compensation (Optional)
                    </label>

                    <div className="grid grid-cols-3 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-600 mb-2">
                          Minimum
                        </label>
                        <input
                          type="number"
                          value={formData.salary.min}
                          onChange={(e) => updateNestedFormData('salary', 'min', e.target.value)}
                          placeholder="50000"
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-600 mb-2">
                          Maximum
                        </label>
                        <input
                          type="number"
                          value={formData.salary.max}
                          onChange={(e) => updateNestedFormData('salary', 'max', e.target.value)}
                          placeholder="80000"
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-600 mb-2">
                          Period
                        </label>
                        <select
                          value={formData.salary.period}
                          onChange={(e) => updateNestedFormData('salary', 'period', e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        >
                          <option value="yearly">Yearly</option>
                          <option value="monthly">Monthly</option>
                          <option value="hourly">Hourly</option>
                        </select>
                      </div>
                    </div>

                    <div className="w-32">
                      <label className="block text-sm font-medium text-gray-600 mb-2">
                        Currency
                      </label>
                      <select
                        value={formData.salary.currency}
                        onChange={(e) => updateNestedFormData('salary', 'currency', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      >
                        <option value="USD">USD</option>
                        <option value="KES">KES</option>
                        <option value="GHS">GHS</option>
                        <option value="EGP">EGP</option>
                        <option value="ZAR">ZAR</option>
                        <option value="NGN">NGN</option>
                        <option value="EUR">EUR</option>
                      </select>
                    </div>
                  </div>
                </div>
              )}

              {/* Step 3: Requirements */}
              {currentStep === 3 && (
                <div className="space-y-6">
                  <div>
                    <h2 className="text-lg font-semibold text-gray-900 mb-4">Requirements & Skills</h2>
                    <p className="text-gray-600 mb-6">
                      Define what qualifications and skills candidates need for this role.
                    </p>
                  </div>

                  {/* Requirements */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Job Requirements *
                    </label>
                    <p className="text-sm text-gray-500 mb-3">
                      List the essential qualifications, experience, and requirements for this position.
                    </p>
                    {formData.requirements.map((requirement, index) => (
                      <div key={index} className="flex items-center space-x-2 mb-2">
                        <input
                          type="text"
                          value={requirement}
                          onChange={(e) => updateArrayItem('requirements', index, e.target.value)}
                          placeholder="e.g. Bachelor's degree in Aerospace Engineering"
                          className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        />
                        {formData.requirements.length > 1 && (
                          <button
                            type="button"
                            onClick={() => removeArrayItem('requirements', index)}
                            className="p-2 text-red-600 hover:text-red-800"
                          >
                            <X className="h-4 w-4" />
                          </button>
                        )}
                      </div>
                    ))}
                    <button
                      type="button"
                      onClick={() => addArrayItem('requirements')}
                      className="flex items-center text-blue-600 hover:text-blue-800 text-sm"
                    >
                      <Plus className="h-4 w-4 mr-1" />
                      Add Requirement
                    </button>
                  </div>

                  {/* Skills Required */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Skills Required *
                    </label>
                    <p className="text-sm text-gray-500 mb-3">
                      List the technical and soft skills needed for this role.
                    </p>
                    {formData.skillsRequired.map((skill, index) => (
                      <div key={index} className="flex items-center space-x-2 mb-2">
                        <input
                          type="text"
                          value={skill}
                          onChange={(e) => updateArrayItem('skillsRequired', index, e.target.value)}
                          placeholder="e.g. Python, MATLAB, Project Management"
                          className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        />
                        {formData.skillsRequired.length > 1 && (
                          <button
                            type="button"
                            onClick={() => removeArrayItem('skillsRequired', index)}
                            className="p-2 text-red-600 hover:text-red-800"
                          >
                            <X className="h-4 w-4" />
                          </button>
                        )}
                      </div>
                    ))}
                    <button
                      type="button"
                      onClick={() => addArrayItem('skillsRequired')}
                      className="flex items-center text-blue-600 hover:text-blue-800 text-sm"
                    >
                      <Plus className="h-4 w-4 mr-1" />
                      Add Skill
                    </button>
                  </div>

                  {/* Responsibilities */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Key Responsibilities (Optional)
                    </label>
                    <p className="text-sm text-gray-500 mb-3">
                      Outline the main duties and responsibilities for this position.
                    </p>
                    {formData.responsibilities.map((responsibility, index) => (
                      <div key={index} className="flex items-center space-x-2 mb-2">
                        <input
                          type="text"
                          value={responsibility}
                          onChange={(e) => updateArrayItem('responsibilities', index, e.target.value)}
                          placeholder="e.g. Design and test satellite communication systems"
                          className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        />
                        {formData.responsibilities.length > 1 && (
                          <button
                            type="button"
                            onClick={() => removeArrayItem('responsibilities', index)}
                            className="p-2 text-red-600 hover:text-red-800"
                          >
                            <X className="h-4 w-4" />
                          </button>
                        )}
                      </div>
                    ))}
                    <button
                      type="button"
                      onClick={() => addArrayItem('responsibilities')}
                      className="flex items-center text-blue-600 hover:text-blue-800 text-sm"
                    >
                      <Plus className="h-4 w-4 mr-1" />
                      Add Responsibility
                    </button>
                  </div>
                </div>
              )}

              {/* Step 4: Additional Information */}
              {currentStep === 4 && (
                <div className="space-y-6">
                  <div>
                    <h2 className="text-lg font-semibold text-gray-900 mb-4">Additional Information</h2>
                    <p className="text-gray-600 mb-6">
                      Add benefits, deadlines, and other important details.
                    </p>
                  </div>

                  {/* Benefits */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Benefits & Perks (Optional)
                    </label>
                    <p className="text-sm text-gray-500 mb-3">
                      List the benefits and perks offered with this position.
                    </p>
                    {formData.benefits.map((benefit, index) => (
                      <div key={index} className="flex items-center space-x-2 mb-2">
                        <input
                          type="text"
                          value={benefit}
                          onChange={(e) => updateArrayItem('benefits', index, e.target.value)}
                          placeholder="e.g. Health insurance, Remote work flexibility"
                          className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        />
                        {formData.benefits.length > 1 && (
                          <button
                            type="button"
                            onClick={() => removeArrayItem('benefits', index)}
                            className="p-2 text-red-600 hover:text-red-800"
                          >
                            <X className="h-4 w-4" />
                          </button>
                        )}
                      </div>
                    ))}
                    <button
                      type="button"
                      onClick={() => addArrayItem('benefits')}
                      className="flex items-center text-blue-600 hover:text-blue-800 text-sm"
                    >
                      <Plus className="h-4 w-4 mr-1" />
                      Add Benefit
                    </button>
                  </div>

                  {/* Application Deadline */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Application Deadline (Optional)
                    </label>
                    <input
                      type="date"
                      value={formData.applicationDeadline}
                      onChange={(e) => updateFormData('applicationDeadline', e.target.value)}
                      min={new Date().toISOString().split('T')[0]}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                    <p className="text-sm text-gray-500 mt-1">
                      When should applications close? Leave blank for no deadline.
                    </p>
                  </div>

                  {/* Start Date */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Expected Start Date (Optional)
                    </label>
                    <input
                      type="date"
                      value={formData.startDate}
                      onChange={(e) => updateFormData('startDate', e.target.value)}
                      min={new Date().toISOString().split('T')[0]}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                    <p className="text-sm text-gray-500 mt-1">
                      When would you like the successful candidate to start?
                    </p>
                  </div>
                </div>
              )}

              {/* Navigation Buttons */}
              <div className="flex justify-between pt-6 border-t border-gray-200">
                <div>
                  {currentStep > 1 && (
                    <button
                      type="button"
                      onClick={prevStep}
                      className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
                    >
                      Previous
                    </button>
                  )}
                </div>
                <div className="flex space-x-3">
                  {currentStep < totalSteps ? (
                    <button
                      type="button"
                      onClick={nextStep}
                      className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                    >
                      Next
                    </button>
                  ) : (
                    <div className="flex space-x-3">
                      <button
                        type="button"
                        onClick={() => handleSubmit(true)}
                        disabled={isSubmitting}
                        className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors disabled:opacity-50"
                      >
                        Save Draft
                      </button>
                      <button
                        type="button"
                        onClick={() => handleSubmit(false)}
                        disabled={isSubmitting || !validateStep(1)}
                        className="px-6 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors disabled:opacity-50 flex items-center"
                      >
                        <Briefcase className="h-4 w-4 mr-2" />
                        {isSubmitting ? 'Publishing...' : 'Publish Job'}
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </RoleGuard>
  );
}