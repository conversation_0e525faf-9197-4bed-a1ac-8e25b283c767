import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { connectDB } from '@/lib/mongodb';
import { User } from '@/models/User';
import { Per<PERSON><PERSON><PERSON><PERSON>, UserRole } from '@/lib/permissions';

export interface AuthenticatedRequest extends NextRequest {
  user?: {
    id: string;
    email: string;
    name: string;
    role: UserRole;
    isActive: boolean;
  };
}

export interface PermissionOptions {
  requiredPermissions?: string[];
  requiredRole?: UserRole | UserRole[];
  allowSelf?: boolean; // Allow if user is accessing their own resource
  resourceIdParam?: string; // Parameter name for resource ID (e.g., 'id', 'userId')
  resourceOwnerField?: string; // Field name that contains the owner ID
}

/**
 * Authentication middleware - ensures user is logged in
 */
export async function requireAuth(
  request: NextRequest,
  handler: (req: AuthenticatedRequest) => Promise<NextResponse>
): Promise<NextResponse> {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    await connectDB();

    // Get full user details
    const user = await User.findById(session.user.id);
    if (!user || !user.isActive) {
      return NextResponse.json(
        { success: false, error: 'User not found or inactive' },
        { status: 401 }
      );
    }

    // Add user to request
    const authenticatedRequest = request as AuthenticatedRequest;
    authenticatedRequest.user = {
      id: user._id.toString(),
      email: user.email,
      name: user.name,
      role: user.role as UserRole,
      isActive: user.isActive,
    };

    return handler(authenticatedRequest);
  } catch (error) {
    console.error('Authentication middleware error:', error);
    return NextResponse.json(
      { success: false, error: 'Authentication failed' },
      { status: 500 }
    );
  }
}

/**
 * Permission middleware - ensures user has required permissions
 */
export async function requirePermissions(
  options: PermissionOptions,
  handler: (req: AuthenticatedRequest) => Promise<NextResponse>
) {
  return async (request: AuthenticatedRequest): Promise<NextResponse> => {
    try {
      if (!request.user) {
        return NextResponse.json(
          { success: false, error: 'Authentication required' },
          { status: 401 }
        );
      }

      await connectDB();

      const user = await User.findById(request.user.id);
      if (!user) {
        return NextResponse.json(
          { success: false, error: 'User not found' },
          { status: 401 }
        );
      }

      // Check role requirements
      if (options.requiredRole) {
        const requiredRoles = Array.isArray(options.requiredRole)
          ? options.requiredRole
          : [options.requiredRole];

        if (!requiredRoles.includes(user.role as UserRole)) {
          return NextResponse.json(
            { success: false, error: 'Insufficient role permissions' },
            { status: 403 }
          );
        }
      }

      // Check specific permissions
      if (options.requiredPermissions) {
        const hasPermissions = PermissionChecker.hasAllPermissions(
          user,
          options.requiredPermissions
        );

        if (!hasPermissions) {
          return NextResponse.json(
            { success: false, error: 'Insufficient permissions' },
            { status: 403 }
          );
        }
      }

      // Check if user is accessing their own resource
      if (options.allowSelf && options.resourceIdParam) {
        const url = new URL(request.url);
        const pathSegments = url.pathname.split('/');
        const resourceId = pathSegments[pathSegments.indexOf(options.resourceIdParam) + 1] ||
                          url.searchParams.get(options.resourceIdParam);

        if (resourceId === user._id.toString()) {
          return handler(request);
        }
      }

      return handler(request);
    } catch (error) {
      console.error('Permission middleware error:', error);
      return NextResponse.json(
        { success: false, error: 'Permission check failed' },
        { status: 500 }
      );
    }
  };
}

/**
 * Admin-only middleware
 */
export async function requireAdmin(
  request: NextRequest,
  handler: (req: AuthenticatedRequest) => Promise<NextResponse>
): Promise<NextResponse> {
  return requireAuth(request,
    requirePermissions(
      { requiredRole: 'admin' },
      handler
    )
  );
}

/**
 * Resource ownership middleware - checks if user owns the resource
 */
export async function requireResourceOwnership(
  resourceModel: any,
  ownerField: string = 'userId',
  handler: (req: AuthenticatedRequest, resource: any) => Promise<NextResponse>
) {
  return async (request: AuthenticatedRequest, { params }: { params: { id: string } }): Promise<NextResponse> => {
    try {
      if (!request.user) {
        return NextResponse.json(
          { success: false, error: 'Authentication required' },
          { status: 401 }
        );
      }

      await connectDB();

      const resource = await resourceModel.findById(params.id);
      if (!resource) {
        return NextResponse.json(
          { success: false, error: 'Resource not found' },
          { status: 404 }
        );
      }

      // Check ownership (admins can access everything)
      const isOwner = resource[ownerField]?.toString() === request.user.id;
      const isAdmin = request.user.role === 'admin';

      if (!isOwner && !isAdmin) {
        return NextResponse.json(
          { success: false, error: 'Access denied' },
          { status: 403 }
        );
      }

      return handler(request, resource);
    } catch (error) {
      console.error('Resource ownership middleware error:', error);
      return NextResponse.json(
        { success: false, error: 'Access check failed' },
        { status: 500 }
      );
    }
  };
}

/**
 * Role-based access control decorator
 */
export function withRoleAccess(allowedRoles: UserRole[]) {
  return function (
    target: any,
    propertyName: string,
    descriptor: PropertyDescriptor
  ) {
    const method = descriptor.value;

    descriptor.value = async function (request: AuthenticatedRequest, ...args: any[]) {
      if (!request.user) {
        return NextResponse.json(
          { success: false, error: 'Authentication required' },
          { status: 401 }
        );
      }

      if (!allowedRoles.includes(request.user.role)) {
        return NextResponse.json(
          { success: false, error: 'Insufficient role permissions' },
          { status: 403 }
        );
      }

      return method.apply(this, [request, ...args]);
    };
  };
}



/**
 * Higher-order function to create protected API handlers
 */
export function createProtectedHandler(options: PermissionOptions = {}) {
  return function (handler: (req: AuthenticatedRequest) => Promise<NextResponse>) {
    return async (request: NextRequest) => {
      return requireAuth(request, requirePermissions(options, handler));
    };
  };
}

// Export commonly used permission combinations
export const ADMIN_ONLY = { requiredRole: 'admin' as UserRole };
export const MENTOR_OR_ADMIN = { requiredRole: ['mentor', 'admin'] as UserRole[] };
export const ORGANISATION_OR_ADMIN = { requiredRole: ['organisation', 'admin'] as UserRole[] };
export const AUTHENTICATED_ONLY = {};

export default {
  requireAuth,
  requirePermissions,
  requireAdmin,
  requireResourceOwnership,
  createProtectedHandler,
};
