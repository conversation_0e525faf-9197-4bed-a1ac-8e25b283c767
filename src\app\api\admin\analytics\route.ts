import { NextRequest, NextResponse } from 'next/server';
import { requireAdmin } from '@/lib/auth-middleware';
import connectDB from '@/lib/mongodb';
import User from '@/models/User';
import Course from '@/models/Course';
import Job from '@/models/Job';
import Enrollment from '@/models/Enrollment';

export const GET = (request: NextRequest) => requireAdmin(request, async (req) => {
  try {
    await connectDB();

    // Get current date for monthly calculations
    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);

    // Parallel queries for better performance
    const [
      totalUsers,
      totalCourses,
      totalJobs,
      totalEnrollments,
      activeUsers,
      newUsersThisMonth,
    ] = await Promise.all([
      // Total users count
      User.countDocuments({ isActive: true }),

      // Total courses count
      Course.countDocuments({ isActive: true }),

      // Total jobs count
      Job.countDocuments({ isActive: true }),

      // Total enrollments count
      Enrollment.countDocuments({ isActive: true }),

      // Active users (logged in within last 30 days)
      User.countDocuments({
        isActive: true,
        lastLogin: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }
      }),

      // New users this month
      User.countDocuments({
        isActive: true,
        createdAt: { $gte: startOfMonth }
      }),
    ]);

    // Additional analytics data
    const usersByRole = await User.aggregate([
      { $match: { isActive: true } },
      { $group: { _id: '$role', count: { $sum: 1 } } }
    ]);

    const coursesByCategory = await Course.aggregate([
      { $match: { isActive: true } },
      { $group: { _id: '$category', count: { $sum: 1 } } }
    ]);

    const enrollmentsByMonth = await Enrollment.aggregate([
      {
        $match: {
          isActive: true,
          createdAt: { $gte: new Date(Date.now() - 6 * 30 * 24 * 60 * 60 * 1000) }
        }
      },
      {
        $group: {
          _id: {
            year: { $year: '$createdAt' },
            month: { $month: '$createdAt' }
          },
          count: { $sum: 1 }
        }
      },
      { $sort: { '_id.year': 1, '_id.month': 1 } }
    ]);

    const topCourses = await Enrollment.aggregate([
      { $match: { isActive: true } },
      { $group: { _id: '$courseId', enrollments: { $sum: 1 } } },
      { $sort: { enrollments: -1 } },
      { $limit: 5 },
      {
        $lookup: {
          from: 'courses',
          localField: '_id',
          foreignField: '_id',
          as: 'course'
        }
      },
      { $unwind: '$course' },
      {
        $project: {
          title: '$course.title',
          enrollments: 1,
          category: '$course.category'
        }
      }
    ]);

    return NextResponse.json({
      success: true,
      data: {
        totalUsers,
        totalCourses,
        totalJobs,
        totalEnrollments,
        activeUsers,
        newUsersThisMonth,
        analytics: {
          usersByRole: usersByRole.reduce((acc, item) => {
            acc[item._id] = item.count;
            return acc;
          }, {}),
          coursesByCategory: coursesByCategory.reduce((acc, item) => {
            acc[item._id] = item.count;
            return acc;
          }, {}),
          enrollmentsByMonth,
          topCourses,
        }
      }
    });

  } catch (error) {
    console.error('Error fetching admin analytics:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch analytics data' },
      { status: 500 }
    );
  }
});
