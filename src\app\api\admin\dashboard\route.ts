import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import connectDB from '@/lib/mongodb';
import { User } from '@/models/User';
import { Course } from '@/models/Course';
import { Enrollment } from '@/models/Enrollment';
import { Job } from '@/models/Job';
import { MentorshipSession } from '@/models/Mentorship';
import { Event } from '@/models/Event';
import { ForumPost } from '@/models/Community';
import { Certificate } from '@/models/Certificate';
import { Resource } from '@/models/Resource';

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    await connectDB();

    // Check if user is admin
    const user = await User.findById(session.user.id);
    if (!user || user.role !== 'admin') {
      return NextResponse.json(
        { success: false, error: 'Access denied' },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const period = searchParams.get('period') || '30'; // days
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - parseInt(period));

    // Get overview statistics
    const [
      totalUsers,
      totalCourses,
      totalEnrollments,
      totalJobs,
      totalMentorshipSessions,
      totalEvents,
      totalForumPosts,
      totalCertificates,
      totalResources,
      newUsersThisPeriod,
      newEnrollmentsThisPeriod,
      completedCoursesThisPeriod,
      activeMentorshipSessions,
      upcomingEvents,
    ] = await Promise.all([
      User.countDocuments({ isActive: true }),
      Course.countDocuments({ isPublished: true }),
      Enrollment.countDocuments({ isActive: true }),
      Job.countDocuments({ isActive: true }),
      MentorshipSession.countDocuments(),
      Event.countDocuments({ status: 'published' }),
      ForumPost.countDocuments(),
      Certificate.countDocuments({ status: 'issued' }),
      Resource.countDocuments({ status: 'published' }),
      User.countDocuments({
        createdAt: { $gte: startDate },
        isActive: true
      }),
      Enrollment.countDocuments({
        createdAt: { $gte: startDate },
        isActive: true
      }),
      Enrollment.countDocuments({
        completedAt: { $gte: startDate, $ne: null }
      }),
      MentorshipSession.countDocuments({
        status: { $in: ['scheduled', 'in_progress'] }
      }),
      Event.countDocuments({
        status: 'published',
        'schedule.startDate': { $gte: new Date() }
      }),
    ]);

    // Get user growth data (last 30 days)
    const userGrowthData = await User.aggregate([
      {
        $match: {
          createdAt: { $gte: startDate },
          isActive: true,
        },
      },
      {
        $group: {
          _id: {
            $dateToString: { format: '%Y-%m-%d', date: '$createdAt' },
          },
          count: { $sum: 1 },
        },
      },
      { $sort: { _id: 1 } },
    ]);

    // Get enrollment data by course category
    const enrollmentsByCategory = await Enrollment.aggregate([
      {
        $lookup: {
          from: 'courses',
          localField: 'courseId',
          foreignField: '_id',
          as: 'course',
        },
      },
      { $unwind: '$course' },
      {
        $group: {
          _id: '$course.category',
          count: { $sum: 1 },
        },
      },
      { $sort: { count: -1 } },
    ]);

    // Get top performing courses
    const topCourses = await Course.aggregate([
      {
        $lookup: {
          from: 'enrollments',
          localField: '_id',
          foreignField: 'courseId',
          as: 'enrollments',
        },
      },
      {
        $addFields: {
          enrollmentCount: { $size: '$enrollments' },
          completionRate: {
            $cond: {
              if: { $gt: [{ $size: '$enrollments' }, 0] },
              then: {
                $multiply: [
                  {
                    $divide: [
                      {
                        $size: {
                          $filter: {
                            input: '$enrollments',
                            cond: { $ne: ['$$this.completedAt', null] },
                          },
                        },
                      },
                      { $size: '$enrollments' },
                    ],
                  },
                  100,
                ],
              },
              else: 0,
            },
          },
        },
      },
      {
        $project: {
          title: 1,
          category: 1,
          enrollmentCount: 1,
          completionRate: 1,
          'ratings.average': 1,
        },
      },
      { $sort: { enrollmentCount: -1 } },
      { $limit: 10 },
    ]);

    // Get user role distribution
    const userRoleDistribution = await User.aggregate([
      {
        $match: { isActive: true },
      },
      {
        $group: {
          _id: '$role',
          count: { $sum: 1 },
        },
      },
    ]);

    // Get recent activities
    const recentActivities = await Promise.all([
      // Recent enrollments
      Enrollment.find({ createdAt: { $gte: startDate } })
        .populate('userId', 'name email')
        .populate('courseId', 'title')
        .sort({ createdAt: -1 })
        .limit(5),

      // Recent forum posts
      ForumPost.find({ createdAt: { $gte: startDate } })
        .populate('authorId', 'name email')
        .sort({ createdAt: -1 })
        .limit(5),

      // Recent mentorship sessions
      MentorshipSession.find({ createdAt: { $gte: startDate } })
        .populate('mentorId', 'name email')
        .populate('menteeId', 'name email')
        .sort({ createdAt: -1 })
        .limit(5),
    ]);

    // Get pending approvals
    const pendingApprovals = {
      courses: await Course.countDocuments({ isPublished: false }),
      resources: await Resource.countDocuments({ status: 'under_review' }),
      events: await Event.countDocuments({ status: 'draft' }),
      mentors: await User.countDocuments({
        'mentorship.isMentor': true,
        'mentorship.isApproved': false
      }),
    };

    // Calculate engagement metrics
    const engagementMetrics = {
      averageSessionDuration: 45, // This would come from analytics
      dailyActiveUsers: Math.floor(totalUsers * 0.15), // Estimated
      courseCompletionRate: totalEnrollments > 0 ?
        Math.round((completedCoursesThisPeriod / totalEnrollments) * 100) : 0,
      forumEngagementRate: Math.round((totalForumPosts / totalUsers) * 100),
    };

    const dashboardData = {
      overview: {
        totalUsers,
        totalCourses,
        totalEnrollments,
        totalJobs,
        totalMentorshipSessions,
        totalEvents,
        totalForumPosts,
        totalCertificates,
        totalResources,
        newUsersThisPeriod,
        newEnrollmentsThisPeriod,
        completedCoursesThisPeriod,
        activeMentorshipSessions,
        upcomingEvents,
      },
      charts: {
        userGrowth: userGrowthData,
        enrollmentsByCategory,
        userRoleDistribution,
      },
      topCourses,
      recentActivities: {
        enrollments: recentActivities[0],
        forumPosts: recentActivities[1],
        mentorshipSessions: recentActivities[2],
      },
      pendingApprovals,
      engagementMetrics,
    };

    return NextResponse.json({
      success: true,
      data: dashboardData,
    });
  } catch (error) {
    console.error('Error fetching admin dashboard data:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch dashboard data' },
      { status: 500 }
    );
  }
}
