'use client';

import React from 'react';
import { EmployerNavbar } from './EmployerNavbar';

interface EmployerLayoutProps {
  children: React.ReactNode;
  newApplications?: number;
  activeJobs?: number;
}

export function EmployerLayout({ 
  children, 
  newApplications = 0, 
  activeJobs = 0 
}: EmployerLayoutProps) {
  return (
    <div className="min-h-screen bg-gray-50">
      <EmployerNavbar 
        newApplications={newApplications} 
        activeJobs={activeJobs} 
      />
      
      {/* Main content */}
      <div className="lg:ml-64">
        <main className="p-4 lg:p-8">
          {children}
        </main>
      </div>
    </div>
  );
}
