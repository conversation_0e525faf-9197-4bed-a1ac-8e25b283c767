import mongoose, { Document, Schema } from 'mongoose';

export interface IEnrollment extends Document {
  _id: string;
  userId: string;
  courseId: string;
  enrolledAt: Date;
  completedAt?: Date;
  progress: {
    completedModules: number;
    totalModules: number;
    completedLessons: string[]; // lesson IDs
    currentModule: number;
    currentLesson: number;
    timeSpent: number; // in minutes
  };
  quiz: {
    attempts: {
      moduleId: string;
      score: number;
      maxScore: number;
      completedAt: Date;
    }[];
    finalScore?: number;
    passed?: boolean;
  };
  certificate?: {
    issued: boolean;
    issuedAt?: Date;
    certificateId?: string;
    downloadUrl?: string;
  };
  rating?: {
    score: number; // 1-5
    review?: string;
    ratedAt: Date;
  };
  isActive: boolean;
  lastAccessedAt: Date;
}

const EnrollmentSchema = new Schema<IEnrollment>({
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  courseId: {
    type: Schema.Types.ObjectId,
    ref: 'Course',
    required: true,
  },
  enrolledAt: {
    type: Date,
    default: Date.now,
  },
  completedAt: Date,
  progress: {
    completedModules: { type: Number, default: 0 },
    totalModules: { type: Number, default: 0 },
    completedLessons: [String],
    currentModule: { type: Number, default: 0 },
    currentLesson: { type: Number, default: 0 },
    timeSpent: { type: Number, default: 0 },
  },
  quiz: {
    attempts: [{
      moduleId: String,
      score: Number,
      maxScore: Number,
      completedAt: { type: Date, default: Date.now },
    }],
    finalScore: Number,
    passed: Boolean,
  },
  certificate: {
    issued: { type: Boolean, default: false },
    issuedAt: Date,
    certificateId: String,
    downloadUrl: String,
  },
  rating: {
    score: {
      type: Number,
      min: 1,
      max: 5,
    },
    review: String,
    ratedAt: Date,
  },
  isActive: { type: Boolean, default: true },
  lastAccessedAt: {
    type: Date,
    default: Date.now,
  },
}, {
  timestamps: true,
});

// Compound index to ensure one enrollment per user per course
EnrollmentSchema.index({ userId: 1, courseId: 1 }, { unique: true });
EnrollmentSchema.index({ userId: 1 });
EnrollmentSchema.index({ courseId: 1 });
EnrollmentSchema.index({ isActive: 1 });
EnrollmentSchema.index({ completedAt: 1 });

const Enrollment = mongoose.models.Enrollment || mongoose.model<IEnrollment>('Enrollment', EnrollmentSchema);

export default Enrollment;
export { Enrollment };
