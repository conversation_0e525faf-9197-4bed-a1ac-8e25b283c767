'use client';

import React from 'react';
import { OrganisationNavbar } from './OrganisationNavbar';

interface OrganisationLayoutProps {
  children: React.ReactNode;
  newApplications?: number;
  activeJobs?: number;
  activeCourses?: number;
  upcomingEvents?: number;
}

export function OrganisationLayout({ 
  children, 
  newApplications = 0, 
  activeJobs = 0,
  activeCourses = 0,
  upcomingEvents = 0
}: OrganisationLayoutProps) {
  return (
    <div className="min-h-screen bg-gray-50">
      <OrganisationNavbar 
        newApplications={newApplications} 
        activeJobs={activeJobs}
        activeCourses={activeCourses}
        upcomingEvents={upcomingEvents}
      />
      
      {/* Main content */}
      <div className="lg:ml-64">
        <main className="p-4 lg:p-8">
          {children}
        </main>
      </div>
    </div>
  );
}
