// Simple script to test API endpoints
const BASE_URL = 'http://localhost:3000';

async function testAPI() {
  console.log('🚀 Testing Nova Platform API Endpoints...\n');

  // Test 1: Get courses
  try {
    console.log('📚 Testing GET /api/courses...');
    const coursesResponse = await fetch(`${BASE_URL}/api/courses`);
    const coursesData = await coursesResponse.json();
    console.log(`✅ Courses API: ${coursesData.success ? 'SUCCESS' : 'FAILED'}`);
    console.log(`   Found ${coursesData.data?.courses?.length || 0} courses\n`);
  } catch (error) {
    console.log('❌ Courses API: FAILED');
    console.log(`   Error: ${error.message}\n`);
  }

  // Test 2: Get jobs
  try {
    console.log('💼 Testing GET /api/jobs...');
    const jobsResponse = await fetch(`${BASE_URL}/api/jobs`);
    const jobsData = await jobsResponse.json();
    console.log(`✅ Jobs API: ${jobsData.success ? 'SUCCESS' : 'FAILED'}`);
    console.log(`   Found ${jobsData.data?.jobs?.length || 0} jobs\n`);
  } catch (error) {
    console.log('❌ Jobs API: FAILED');
    console.log(`   Error: ${error.message}\n`);
  }

  // Test 3: Test user registration
  try {
    console.log('👤 Testing POST /api/auth/register...');
    const registerResponse = await fetch(`${BASE_URL}/api/auth/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        name: 'Test User',
        email: `test${Date.now()}@example.com`,
        password: 'password123',
        role: 'student'
      })
    });
    const registerData = await registerResponse.json();
    console.log(`✅ Registration API: ${registerResponse.ok ? 'SUCCESS' : 'FAILED'}`);
    if (!registerResponse.ok) {
      console.log(`   Error: ${registerData.error}\n`);
    } else {
      console.log(`   User created successfully\n`);
    }
  } catch (error) {
    console.log('❌ Registration API: FAILED');
    console.log(`   Error: ${error.message}\n`);
  }

  // Test 4: Test SMS endpoint (should work but won't send actual SMS in dev)
  try {
    console.log('📱 Testing POST /api/sms/send...');
    const smsResponse = await fetch(`${BASE_URL}/api/sms/send`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        to: '+254700000000',
        type: 'welcome',
        data: { name: 'Test User' }
      })
    });
    const smsData = await smsResponse.json();
    console.log(`✅ SMS API: ${smsData.success ? 'SUCCESS' : 'FAILED'}`);
    if (!smsData.success) {
      console.log(`   Note: ${smsData.error || 'SMS functionality deferred as requested'}\n`);
    } else {
      console.log(`   SMS sent successfully\n`);
    }
  } catch (error) {
    console.log('❌ SMS API: FAILED');
    console.log(`   Error: ${error.message}\n`);
  }

  console.log('🎉 API Testing Complete!');
}

// Run the tests
testAPI().catch(console.error);
