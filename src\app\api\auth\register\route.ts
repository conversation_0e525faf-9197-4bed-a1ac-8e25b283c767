import { NextRequest, NextResponse } from 'next/server';
import bcrypt from 'bcryptjs';
import { MongoClient } from 'mongodb';
import { PhoneValidator } from '@/lib/phone-validation';
import { SMSService } from '@/lib/sms';

const client = new MongoClient(process.env.MONGODB_URI!);
const clientPromise = client.connect();

export async function POST(request: NextRequest) {
  try {
    const { name, email, phone, password, role = 'student' } = await request.json();

    // Validate required fields
    if (!name || !email || !password || !phone) {
      return NextResponse.json(
        { error: 'Name, email, phone, and password are required' },
        { status: 400 }
      );
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { error: 'Invalid email format' },
        { status: 400 }
      );
    }

    // Validate password strength
    if (password.length < 6) {
      return NextResponse.json(
        { error: 'Password must be at least 6 characters long' },
        { status: 400 }
      );
    }

    // Validate phone number (now required)
    if (!PhoneValidator.isValid(phone)) {
      return NextResponse.json(
        { error: 'Invalid phone number format. Please use Kenyan format: +254 XXXXXXXXX' },
        { status: 400 }
      );
    }



    const mongoClient = await clientPromise;
    const db = mongoClient.db();

    // Check if user already exists
    const existingUser = await db.collection('users').findOne({ email });
    if (existingUser) {
      return NextResponse.json(
        { error: 'User with this email already exists' },
        { status: 409 }
      );
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 12);

    // Create user
    const userData = {
      name,
      email,
      phone,
      password: hashedPassword,
      role,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    const result = await db.collection('users').insertOne(userData);

    // Send welcome SMS notification
    try {
      await SMSService.sendWelcomeSMS(phone, name);
      console.log('Welcome SMS sent successfully to:', phone);
    } catch (smsError) {
      console.error('Failed to send welcome SMS:', smsError);
      // Don't fail the registration if SMS fails
    }

    return NextResponse.json(
      {
        message: 'User created successfully',
        userId: result.insertedId,
        user: {
          id: result.insertedId,
          name,
          email,
          role
        }
      },
      { status: 201 }
    );

  } catch (error) {
    console.error('Registration error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
