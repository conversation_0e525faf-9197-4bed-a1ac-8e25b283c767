# 🌱 Nova Platform - Comprehensive Seed Data Guide

This guide explains the comprehensive seed data created for the Nova platform, including organizations, mentors, jobs, events, courses, and educational resources.

## 📊 Overview

The seed data includes:
- **3 Organizations** with unified 'organisation' user type
- **3 Mentors** with detailed profiles and expertise
- **9 Job Postings** (3 per organization)
- **8 Events** (2-3 per organization)
- **3 Enhanced Courses** with detailed modules
- **6 Educational Resources** (2 per organization)
- **3 Detailed Mentor Profiles** with availability and pricing

## 🏢 Organizations

### 1. African Space Research Institute (ASRI)
- **Email**: `<EMAIL>`
- **Focus**: Research & Education
- **Location**: Nairobi, Kenya
- **Industry**: Space Technology & Research
- **Size**: 50-100 employees
- **Specialties**: Satellite technology, space science education, capacity building

### 2. Stellar Dynamics Corporation
- **Email**: `<EMAIL>`
- **Focus**: Commercial Space Technology
- **Location**: Accra, Ghana
- **Industry**: Commercial Space Technology
- **Size**: 20-50 employees
- **Specialties**: Small satellite development, launch services, space-based solutions

### 3. Continental Space Alliance (CSA)
- **Email**: `<EMAIL>`
- **Focus**: Policy & International Cooperation
- **Location**: Johannesburg, South Africa
- **Industry**: Space Policy & Cooperation
- **Size**: 100+ employees
- **Specialties**: Space policy development, international cooperation, governance

## 👨‍🏫 Mentors

### 1. Dr. Amara Okafor - Satellite Systems Expert
- **Email**: `<EMAIL>`
- **Organization**: African Space Research Institute
- **Specialization**: Satellite Systems Engineering
- **Experience**: 15+ years
- **Hourly Rate**: $150 USD
- **Availability**: Mon-Fri, 9AM-12PM & 2PM-5PM (Africa/Nairobi)
- **Expertise**: Satellite Engineering, Orbital Mechanics, Mission Design, Earth Observation

### 2. Prof. Kwame Asante - AI/ML Space Applications Expert
- **Email**: `<EMAIL>`
- **Organization**: Stellar Dynamics Corporation
- **Specialization**: AI Applications in Space Technology
- **Experience**: 12+ years
- **Hourly Rate**: $120 USD
- **Availability**: Mon-Thu, Sat, 8AM-11AM & 3PM-6PM (Africa/Accra)
- **Expertise**: AI, Machine Learning, Satellite Data Analysis, Python Programming

### 3. Dr. Fatima Al-Rashid - Cybersecurity & Space Law Expert
- **Email**: `<EMAIL>`
- **Organization**: Continental Space Alliance
- **Specialization**: Space Security and International Law
- **Experience**: 10+ years
- **Hourly Rate**: $180 USD
- **Availability**: Tue-Fri, 10AM-1PM & 3PM-5PM (Africa/Johannesburg)
- **Expertise**: Cybersecurity, Space Law, International Relations, Policy Development

## 💼 Job Postings

### African Space Research Institute (3 jobs)
1. **Senior Satellite Systems Engineer** - $85K-$125K USD
2. **Space Technology Research Fellow** - $60K-$80K USD
3. **Educational Program Coordinator** - $45K-$65K USD

### Stellar Dynamics Corporation (3 jobs)
1. **Small Satellite Design Engineer** - $70K-$95K USD
2. **Launch Operations Specialist** - $65K-$85K USD
3. **Business Development Manager** - $80K-$120K USD

### Continental Space Alliance (2 jobs)
1. **Space Policy Analyst** - $55K-$75K USD
2. **International Cooperation Coordinator** - $70K-$95K USD

## 🎯 Events

### African Space Research Institute (3 events)
1. **African Satellite Technology Summit 2024** - Conference (Mar 15-17)
2. **Earth Observation Data Analysis Workshop** - Workshop (Feb 20-22)
3. **Women in Space Technology Networking Event** - Networking (Mar 8)

### Stellar Dynamics Corporation (2 events)
1. **CubeSat Development Bootcamp** - Workshop (Apr 8-12)
2. **Commercial Space Industry Forum** - Conference (May 22-23)

### Continental Space Alliance (2 events)
1. **African Space Policy Symposium** - Conference (Jun 10-12)
2. **International Space Cooperation Workshop** - Workshop (Jul 15-17)

## 📚 Educational Resources

### African Space Research Institute (2 resources)
1. **Satellite Systems Engineering Handbook** - PDF (450 pages)
2. **Earth Observation Data Processing Tutorial Series** - Video (5 hours)

### Stellar Dynamics Corporation (2 resources)
1. **CubeSat Design and Development Guide** - PDF (180 pages)
2. **Commercial Space Business Models Presentation** - Presentation (85 slides)

### Continental Space Alliance (2 resources)
1. **African Space Policy Framework Document** - Document (120 pages)
2. **International Space Law Reference Database** - Dataset (100MB)

## 🚀 How to Use the Seed Data

### Method 1: Using the Script (Recommended)
```bash
# Run the seeding script
pnpm seed
```

### Method 2: Using the API Endpoint
```bash
# Make a POST request to the seed endpoint
curl -X POST http://localhost:3000/api/seed
```

### Method 3: Programmatically
```typescript
import { seedDatabase } from '@/lib/seed-data';

// Call the function directly
await seedDatabase();
```

## 🔑 Login Credentials

All users have the password: `password123`

### Organizations
- `<EMAIL>` - African Space Research Institute
- `<EMAIL>` - Stellar Dynamics Corporation
- `<EMAIL>` - Continental Space Alliance

### Mentors
- `<EMAIL>` - Dr. Amara Okafor
- `<EMAIL>` - Prof. Kwame Asante
- `<EMAIL>` - Dr. Fatima Al-Rashid

## 🎯 Testing Scenarios

### Organization Features
1. **Job Management**: Login as any organization to post, edit, and manage jobs
2. **Event Organization**: Create and manage events, workshops, and conferences
3. **Resource Sharing**: Upload and manage educational resources
4. **Analytics**: View job applications, event registrations, and resource downloads

### Mentor Features
1. **Profile Management**: Complete mentor profiles with expertise and availability
2. **Session Scheduling**: Set availability and manage mentoring sessions
3. **Course Creation**: Create and manage educational courses
4. **Student Interaction**: Engage with students and provide guidance

### Student Experience
1. **Job Applications**: Browse and apply for jobs from different organizations
2. **Event Registration**: Register for events, workshops, and networking sessions
3. **Learning**: Access courses, resources, and educational materials
4. **Mentorship**: Connect with mentors and schedule sessions

## 📈 Data Relationships

The seed data is carefully structured with proper relationships:
- **Users** are linked to their **Organizations** and **Mentor Profiles**
- **Jobs** are associated with **Organization Users**
- **Events** have **Organizers** linked to **Organizations**
- **Courses** are taught by **Mentor Users**
- **Resources** are authored by **Users** from specific **Organizations**
- **Mentor Profiles** contain detailed availability, pricing, and expertise data

## 🔄 Updating Seed Data

To modify the seed data:
1. Edit the relevant arrays in `src/lib/seed-data.ts`
2. Run the seeding script again: `pnpm seed`
3. The script will clear existing data and create fresh records

## ⚙️ Configuration Options

The seed data system supports flexible configuration to control what data gets created. This is particularly useful for testing scenarios where you want to skip certain resource-intensive or complex entity creation.

### Environment Variables

You can control seed data creation using these environment variables:

```bash
# Skip specific entity types
SEED_SKIP_RESOURCES=true          # Skip creating resources
SEED_SKIP_MENTOR_PROFILES=true    # Skip creating mentor profiles

# Minimal mode - only create essential data (users and courses)
SEED_MINIMAL_MODE=true            # Enables minimal seeding mode
```

### Configuration Examples

#### Testing Mode (Fast seeding)
```bash
# .env.local
SEED_SKIP_RESOURCES=true
SEED_SKIP_MENTOR_PROFILES=true
```

#### Minimal Mode (Only users and courses)
```bash
# .env.local
SEED_MINIMAL_MODE=true
```

#### Full Development Mode (Default)
```bash
# .env.local
# No additional variables needed - creates all data
```

### Programmatic Configuration

You can also pass configuration directly when calling the seed function:

```typescript
import { seedDatabase } from '@/lib/seed-data';

// Skip resources and mentor profiles
await seedDatabase({
  skipResources: true,
  skipMentorProfiles: true
});

// Minimal mode
await seedDatabase({
  minimalMode: true
});
```

### API Configuration

When using the API endpoint, you can pass configuration in the request body:

```bash
# Skip resources and mentor profiles
curl -X POST http://localhost:3000/api/seed \
  -H "Content-Type: application/json" \
  -d '{
    "config": {
      "skipResources": true,
      "skipMentorProfiles": true
    }
  }'

# Minimal mode
curl -X POST http://localhost:3000/api/seed \
  -H "Content-Type: application/json" \
  -d '{
    "config": {
      "minimalMode": true
    }
  }'
```

## ⚠️ Important Notes

- **Development Only**: Seeding is only allowed in development environment
- **Data Reset**: Running the seed script will clear all existing data
- **Relationships**: All data relationships are properly maintained
- **Realistic Data**: All information is realistic and representative of actual organizations
- **Unified Role**: All organizations use the 'organisation' role as per platform design
