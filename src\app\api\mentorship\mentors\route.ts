import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { connectDB } from '@/lib/mongodb';
import { MentorProfile } from '@/models/Mentorship';
import { User } from '@/models/User';

export async function GET(request: NextRequest) {
  try {
    await connectDB();

    const { searchParams } = new URL(request.url);
    const expertise = searchParams.get('expertise');
    const specialization = searchParams.get('specialization');
    const minRating = searchParams.get('minRating');
    const maxPrice = searchParams.get('maxPrice');
    const freeOnly = searchParams.get('freeOnly') === 'true';
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '12');
    const skip = (page - 1) * limit;

    // Build query
    const query: any = {
      isApproved: true,
      isActive: true,
    };

    if (expertise) {
      query.expertise = { $in: [expertise] };
    }

    if (specialization) {
      query.specialization = { $regex: specialization, $options: 'i' };
    }

    if (minRating) {
      query.averageRating = { $gte: parseFloat(minRating) };
    }

    if (maxPrice) {
      query['pricing.hourlyRate'] = { $lte: parseFloat(maxPrice) };
    }

    if (freeOnly) {
      query['pricing.offersFreeSession'] = true;
    }

    const mentors = await MentorProfile.find(query)
      .populate('userId', 'name email avatar profile')
      .sort({ averageRating: -1, totalSessions: -1 })
      .skip(skip)
      .limit(limit);

    const total = await MentorProfile.countDocuments(query);

    // Get unique expertise areas for filtering
    const expertiseAreas = await MentorProfile.distinct('expertise', {
      isApproved: true,
      isActive: true,
    });

    return NextResponse.json({
      success: true,
      data: {
        mentors,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
        filters: {
          expertiseAreas: expertiseAreas.flat().filter((item, index, arr) => arr.indexOf(item) === index),
        },
      },
    });
  } catch (error) {
    console.error('Error fetching mentors:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch mentors' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    await connectDB();

    // Check if user already has a mentor profile
    const existingProfile = await MentorProfile.findOne({ userId: session.user.id });
    if (existingProfile) {
      return NextResponse.json(
        { success: false, error: 'Mentor profile already exists' },
        { status: 409 }
      );
    }

    const body = await request.json();
    const {
      expertise,
      specialization,
      experience,
      education,
      certifications,
      languages,
      timezone,
      availability,
      pricing,
      bio,
      achievements,
    } = body;

    // Validate required fields
    if (!expertise || !specialization || !bio || !availability) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Create mentor profile
    const mentorProfile = new MentorProfile({
      userId: session.user.id,
      expertise,
      specialization,
      experience: experience || 0,
      education: education || [],
      certifications: certifications || [],
      languages: languages || ['English'],
      timezone: timezone || 'UTC',
      availability,
      pricing: {
        hourlyRate: pricing?.hourlyRate || 0,
        currency: pricing?.currency || 'USD',
        offersFreeSession: pricing?.offersFreeSession || false,
      },
      bio,
      achievements: achievements || [],
      isApproved: false, // Requires admin approval
    });

    await mentorProfile.save();

    // Update user role to include mentor
    await User.findByIdAndUpdate(session.user.id, {
      'mentorship.isMentor': true,
    });

    return NextResponse.json({
      success: true,
      data: mentorProfile,
      message: 'Mentor profile created successfully. Awaiting approval.',
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating mentor profile:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to create mentor profile' },
      { status: 500 }
    );
  }
}
