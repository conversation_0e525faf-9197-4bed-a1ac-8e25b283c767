import connectDB from './mongodb';
import Course from '@/models/Course';
import Job from '@/models/Job';
import User from '@/models/User';
import { Event } from '@/models/Event';
import { Resource } from '@/models/Resource';
import { MentorProfile } from '@/models/Mentorship';
import * as bcrypt from 'bcryptjs';
import { getEnvironmentConfig, logSeedConfig, type SeedConfig } from './seed-config';

export const sampleCourses = [
  {
    title: 'Introduction to Satellite Engineering',
    description: 'Learn the fundamentals of satellite design, orbital mechanics, and space systems engineering. This comprehensive course covers everything from basic concepts to advanced satellite subsystems.',
    instructor: 'Dr. <PERSON>',
    instructorId: null, // Will be set when we create users
    category: 'Space Technology',
    level: 'beginner',
    duration: 40,
    thumbnail: 'https://images.unsplash.com/photo-1446776653964-20c1d3a81b06?w=400',
    tags: ['satellite', 'engineering', 'space', 'orbital mechanics'],
    modules: [
      {
        title: 'Introduction to Space Systems',
        description: 'Overview of space systems and satellite fundamentals',
        order: 1,
        lessons: [
          {
            title: 'What is a Satellite?',
            type: 'video',
            content: 'https://example.com/video1',
            duration: 30,
            order: 1,
          },
          {
            title: 'Types of Satellites',
            type: 'text',
            content: 'Detailed explanation of different satellite types...',
            duration: 20,
            order: 2,
          },
        ],
      },
    ],
    prerequisites: ['Basic physics knowledge', 'Mathematics fundamentals'],
    learningOutcomes: ['Understand satellite basics', 'Design simple satellite systems'],
    certification: {
      available: true,
      passingScore: 80,
    },
    pricing: {
      isFree: true,
      price: 0,
    },
    enrollment: {
      totalStudents: 1250,
      isOpen: true,
    },
    ratings: {
      average: 4.8,
      totalRatings: 156,
    },
    isPublished: true,
  },
  {
    title: 'AI Applications in Space Exploration',
    description: 'Explore how artificial intelligence is revolutionizing space missions and data analysis. Learn to implement AI algorithms for space applications.',
    instructor: 'Prof. Kwame Asante',
    instructorId: null,
    category: 'AI in Space',
    level: 'intermediate',
    duration: 35,
    thumbnail: 'https://images.unsplash.com/photo-1635070041078-e363dbe005cb?w=400',
    tags: ['AI', 'machine learning', 'space exploration', 'data analysis'],
    modules: [
      {
        title: 'AI Fundamentals for Space',
        description: 'Introduction to AI concepts in space context',
        order: 1,
        lessons: [
          {
            title: 'Machine Learning Basics',
            type: 'video',
            content: 'https://example.com/video2',
            duration: 45,
            order: 1,
          },
        ],
      },
    ],
    prerequisites: ['Programming experience', 'Basic mathematics'],
    learningOutcomes: ['Implement AI algorithms', 'Analyze space data'],
    certification: {
      available: true,
      passingScore: 85,
    },
    pricing: {
      isFree: false,
      price: 99,
    },
    enrollment: {
      totalStudents: 890,
      isOpen: true,
    },
    ratings: {
      average: 4.9,
      totalRatings: 89,
    },
    isPublished: true,
  },
  {
    title: 'Cybersecurity for Space Systems',
    description: 'Understand the unique security challenges and solutions for space-based infrastructure. Learn to protect satellite systems from cyber threats.',
    instructor: 'Dr. Fatima Al-Rashid',
    instructorId: null,
    category: 'Cybersecurity',
    level: 'advanced',
    duration: 50,
    thumbnail: 'https://images.unsplash.com/photo-1563206767-5b18f218e8de?w=400',
    tags: ['cybersecurity', 'space systems', 'satellite security'],
    modules: [
      {
        title: 'Space Cybersecurity Fundamentals',
        description: 'Understanding security challenges in space',
        order: 1,
        lessons: [
          {
            title: 'Threat Landscape in Space',
            type: 'video',
            content: 'https://example.com/video3',
            duration: 40,
            order: 1,
          },
        ],
      },
    ],
    prerequisites: ['Cybersecurity basics', 'Network fundamentals'],
    learningOutcomes: ['Secure space systems', 'Implement security protocols'],
    certification: {
      available: true,
      passingScore: 90,
    },
    pricing: {
      isFree: false,
      price: 149,
    },
    enrollment: {
      totalStudents: 650,
      isOpen: true,
    },
    ratings: {
      average: 4.7,
      totalRatings: 67,
    },
    isPublished: true,
  },
];

export const sampleJobs = [
  // African Space Research Institute Jobs
  {
    title: 'Senior Satellite Systems Engineer',
    company: 'African Space Research Institute',
    employerId: null, // Will be set when we create users
    description: 'Lead the design and development of next-generation Earth observation satellites. Work with cutting-edge technology to advance Africa\'s space capabilities and mentor junior engineers in satellite subsystem design.',
    requirements: [
      'Master\'s degree in Aerospace/Electrical Engineering',
      '5+ years experience in satellite systems',
      'MATLAB/Simulink proficiency',
      'Experience with satellite subsystems (ADCS, Power, Communications)',
      'Knowledge of space environment and orbital mechanics'
    ],
    responsibilities: [
      'Lead satellite subsystem design and integration',
      'Conduct system-level analysis and verification',
      'Mentor junior engineers and researchers',
      'Collaborate with international space agencies',
      'Develop technical documentation and reports'
    ],
    type: 'full-time',
    location: {
      type: 'on-site',
      city: 'Nairobi',
      country: 'Kenya',
    },
    salary: {
      min: 85000,
      max: 125000,
      currency: 'USD',
      period: 'yearly',
    },
    category: 'Satellite Engineering',
    tags: ['satellite', 'engineering', 'space systems', 'leadership'],
    skillsRequired: ['MATLAB', 'Simulink', 'Systems Engineering', 'ADCS', 'Power Systems'],
    experienceLevel: 'senior',
    education: ['Master\'s degree in Aerospace/Electrical Engineering'],
    benefits: ['Health insurance', 'Professional development', 'Research opportunities', 'Conference attendance'],
    isActive: true,
    isFeatured: true,
    applications: {
      total: 28,
      shortlisted: 6,
      interviewed: 3,
      hired: 0,
    },
  },
  {
    title: 'Space Technology Research Fellow',
    company: 'African Space Research Institute',
    employerId: null,
    description: 'Conduct cutting-edge research in space technology applications for African development. Focus on Earth observation data analysis, climate monitoring, and agricultural applications of satellite technology.',
    requirements: [
      'PhD in relevant field (Physics, Engineering, Earth Sciences)',
      'Research experience in space technology',
      'Programming skills (Python, R, MATLAB)',
      'Publication record in peer-reviewed journals',
      'Strong analytical and communication skills'
    ],
    responsibilities: [
      'Conduct independent research projects',
      'Publish research findings in top-tier journals',
      'Collaborate with international research teams',
      'Supervise graduate students',
      'Apply for research grants and funding'
    ],
    type: 'full-time',
    location: {
      type: 'hybrid',
      city: 'Nairobi',
      country: 'Kenya',
    },
    salary: {
      min: 60000,
      max: 80000,
      currency: 'USD',
      period: 'yearly',
    },
    category: 'Research & Development',
    tags: ['research', 'space technology', 'earth observation', 'climate'],
    skillsRequired: ['Python', 'Research', 'Data Analysis', 'Academic Writing'],
    experienceLevel: 'senior',
    education: ['PhD in relevant field'],
    benefits: ['Research budget', 'Conference travel', 'Publication support', 'Sabbatical opportunities'],
    isActive: true,
    isFeatured: false,
    applications: {
      total: 15,
      shortlisted: 4,
      interviewed: 2,
      hired: 0,
    },
  },
  {
    title: 'Educational Program Coordinator',
    company: 'African Space Research Institute',
    employerId: null,
    description: 'Develop and manage educational programs in space technology for students and professionals across Africa. Create curriculum, coordinate with universities, and organize training workshops.',
    requirements: [
      'Master\'s degree in Education or STEM field',
      '3+ years experience in educational program management',
      'Knowledge of space technology and applications',
      'Excellent communication and organizational skills',
      'Experience with online learning platforms'
    ],
    responsibilities: [
      'Design and implement educational curricula',
      'Coordinate with partner universities and institutions',
      'Organize workshops and training programs',
      'Manage online learning platforms',
      'Evaluate program effectiveness and outcomes'
    ],
    type: 'full-time',
    location: {
      type: 'hybrid',
      city: 'Nairobi',
      country: 'Kenya',
    },
    salary: {
      min: 45000,
      max: 65000,
      currency: 'USD',
      period: 'yearly',
    },
    category: 'Business & Management',
    tags: ['education', 'program management', 'curriculum development', 'training'],
    skillsRequired: ['Program Management', 'Curriculum Development', 'Communication', 'LMS'],
    experienceLevel: 'mid',
    education: ['Master\'s degree in Education or STEM'],
    benefits: ['Professional development', 'Travel opportunities', 'Flexible schedule', 'Training budget'],
    isActive: true,
    isFeatured: false,
    applications: {
      total: 32,
      shortlisted: 8,
      interviewed: 4,
      hired: 1,
    },
  },

  // Stellar Dynamics Corporation Jobs
  {
    title: 'Small Satellite Design Engineer',
    company: 'Stellar Dynamics Corporation',
    employerId: null,
    description: 'Design and develop innovative small satellite platforms for commercial and government clients. Work on CubeSat and microsatellite projects from concept to deployment.',
    requirements: [
      'Bachelor\'s degree in Aerospace/Mechanical Engineering',
      '3+ years experience in satellite design',
      'CAD software proficiency (SolidWorks, CATIA)',
      'Knowledge of space environment and materials',
      'Experience with satellite testing and qualification'
    ],
    responsibilities: [
      'Design satellite structures and mechanisms',
      'Perform structural analysis and simulations',
      'Develop test procedures and protocols',
      'Support satellite integration and testing',
      'Collaborate with customers on requirements'
    ],
    type: 'full-time',
    location: {
      type: 'on-site',
      city: 'Accra',
      country: 'Ghana',
    },
    salary: {
      min: 70000,
      max: 95000,
      currency: 'USD',
      period: 'yearly',
    },
    category: 'Satellite Engineering',
    tags: ['satellite design', 'cubesat', 'mechanical engineering', 'CAD'],
    skillsRequired: ['SolidWorks', 'CATIA', 'Structural Analysis', 'Satellite Design'],
    experienceLevel: 'mid',
    education: ['Bachelor\'s degree in Aerospace/Mechanical Engineering'],
    benefits: ['Stock options', 'Health insurance', 'Innovation time', 'Startup environment'],
    isActive: true,
    isFeatured: true,
    applications: {
      total: 41,
      shortlisted: 9,
      interviewed: 5,
      hired: 0,
    },
  },
  {
    title: 'Launch Operations Specialist',
    company: 'Stellar Dynamics Corporation',
    employerId: null,
    description: 'Manage launch operations for small satellite missions. Coordinate with launch providers, oversee mission planning, and ensure successful satellite deployment.',
    requirements: [
      'Bachelor\'s degree in Engineering or related field',
      '2+ years experience in aerospace operations',
      'Knowledge of launch vehicle interfaces',
      'Project management experience',
      'Strong problem-solving and communication skills'
    ],
    responsibilities: [
      'Coordinate with launch service providers',
      'Develop mission timelines and procedures',
      'Oversee satellite integration and testing',
      'Monitor launch operations and deployment',
      'Manage customer communications during missions'
    ],
    type: 'full-time',
    location: {
      type: 'hybrid',
      city: 'Accra',
      country: 'Ghana',
    },
    salary: {
      min: 65000,
      max: 85000,
      currency: 'USD',
      period: 'yearly',
    },
    category: 'Business & Management',
    tags: ['launch operations', 'project management', 'mission planning'],
    skillsRequired: ['Project Management', 'Operations', 'Communication', 'Problem Solving'],
    experienceLevel: 'mid',
    education: ['Bachelor\'s degree in Engineering'],
    benefits: ['Travel opportunities', 'Performance bonuses', 'Professional development', 'Flexible hours'],
    isActive: true,
    isFeatured: false,
    applications: {
      total: 25,
      shortlisted: 6,
      interviewed: 3,
      hired: 0,
    },
  },
  {
    title: 'Business Development Manager - Space Services',
    company: 'Stellar Dynamics Corporation',
    employerId: null,
    description: 'Drive business growth by identifying new market opportunities and developing strategic partnerships in the African space sector. Focus on commercial and government clients.',
    requirements: [
      'MBA or Bachelor\'s in Business/Engineering',
      '5+ years experience in business development',
      'Knowledge of space industry and markets',
      'Strong networking and relationship building skills',
      'Experience with government and commercial sales'
    ],
    responsibilities: [
      'Identify and pursue new business opportunities',
      'Develop strategic partnerships and alliances',
      'Manage customer relationships and contracts',
      'Represent company at industry events',
      'Develop market analysis and business cases'
    ],
    type: 'full-time',
    location: {
      type: 'hybrid',
      city: 'Accra',
      country: 'Ghana',
    },
    salary: {
      min: 80000,
      max: 120000,
      currency: 'USD',
      period: 'yearly',
    },
    category: 'Business & Management',
    tags: ['business development', 'sales', 'partnerships', 'space industry'],
    skillsRequired: ['Business Development', 'Sales', 'Networking', 'Market Analysis'],
    experienceLevel: 'senior',
    education: ['MBA or Bachelor\'s in Business/Engineering'],
    benefits: ['Commission structure', 'Travel budget', 'Networking events', 'Stock options'],
    isActive: true,
    isFeatured: true,
    applications: {
      total: 18,
      shortlisted: 5,
      interviewed: 2,
      hired: 0,
    },
  },

  // Continental Space Alliance Jobs
  {
    title: 'Space Policy Analyst',
    company: 'Continental Space Alliance',
    employerId: null,
    description: 'Analyze space policies and regulations across African countries. Develop policy recommendations and support the harmonization of space governance frameworks across the continent.',
    requirements: [
      'Master\'s degree in International Relations, Law, or Policy Studies',
      '3+ years experience in policy analysis',
      'Knowledge of space law and international treaties',
      'Strong research and analytical skills',
      'Excellent written and verbal communication'
    ],
    responsibilities: [
      'Analyze national and international space policies',
      'Develop policy recommendations and frameworks',
      'Support government stakeholders in policy development',
      'Conduct research on space governance issues',
      'Prepare reports and briefing materials'
    ],
    type: 'full-time',
    location: {
      type: 'hybrid',
      city: 'Johannesburg',
      country: 'South Africa',
    },
    salary: {
      min: 55000,
      max: 75000,
      currency: 'USD',
      period: 'yearly',
    },
    category: 'Business & Management',
    tags: ['policy analysis', 'space law', 'governance', 'research'],
    skillsRequired: ['Policy Analysis', 'Research', 'Space Law', 'Communication'],
    experienceLevel: 'mid',
    education: ['Master\'s degree in relevant field'],
    benefits: ['International travel', 'Conference attendance', 'Professional development', 'Diplomatic exposure'],
    isActive: true,
    isFeatured: false,
    applications: {
      total: 22,
      shortlisted: 7,
      interviewed: 3,
      hired: 0,
    },
  },
  {
    title: 'International Cooperation Coordinator',
    company: 'Continental Space Alliance',
    employerId: null,
    description: 'Facilitate international cooperation and partnerships in space activities. Coordinate with space agencies, international organizations, and diplomatic missions.',
    requirements: [
      'Master\'s degree in International Relations or related field',
      '4+ years experience in international cooperation',
      'Knowledge of space sector and international organizations',
      'Multilingual capabilities (English, French, Arabic preferred)',
      'Strong diplomatic and negotiation skills'
    ],
    responsibilities: [
      'Coordinate international space cooperation initiatives',
      'Facilitate partnerships between African and international space agencies',
      'Organize diplomatic meetings and conferences',
      'Develop cooperation agreements and MOUs',
      'Represent the Alliance at international forums'
    ],
    type: 'full-time',
    location: {
      type: 'hybrid',
      city: 'Johannesburg',
      country: 'South Africa',
    },
    salary: {
      min: 70000,
      max: 95000,
      currency: 'USD',
      period: 'yearly',
    },
    category: 'Business & Management',
    tags: ['international cooperation', 'diplomacy', 'partnerships', 'space agencies'],
    skillsRequired: ['Diplomacy', 'Negotiation', 'International Relations', 'Multilingual'],
    experienceLevel: 'senior',
    education: ['Master\'s degree in International Relations'],
    benefits: ['Diplomatic status', 'International travel', 'Language training', 'Networking opportunities'],
    isActive: true,
    isFeatured: true,
    applications: {
      total: 16,
      shortlisted: 4,
      interviewed: 2,
      hired: 0,
    },
  },
];

// Organization Users (with 'organisation' role)
export const sampleOrganizations = [
  {
    email: '<EMAIL>',
    password: 'password123',
    name: 'African Space Research Institute',
    phone: '+254-700-123456',
    role: 'organisation',
    profile: {
      bio: 'Leading research institute advancing space technology across Africa through education, research, and innovation. Established in 2015, we focus on satellite technology, space science education, and capacity building for the African space sector.',
      skills: ['Research', 'Education', 'Satellite Technology', 'Space Science', 'Capacity Building'],
      interests: ['Space Technology', 'Education', 'Research', 'Innovation', 'African Development'],
      education: 'Research Institute',
      experience: '8+ years in space research and education',
      location: 'Nairobi, Kenya',
      currentLevel: 'advanced',
      careerGoals: ['Advance African space capabilities', 'Train next generation of space professionals', 'Foster space innovation'],
    },
    preferences: {
      notifications: {
        email: true,
        sms: true,
        push: false,
      },
      language: 'en',
      timezone: 'Africa/Nairobi',
    },
    progress: {
      coursesCompleted: 0,
      certificatesEarned: 0,
      skillsAcquired: [],
      totalLearningHours: 0,
    },
    mentorship: {
      isMentor: false,
      isMentee: false,
      isApproved: false,
      expertise: [],
      mentees: [],
      mentors: [],
    },
    employment: {
      isEmployer: true,
      company: 'African Space Research Institute',
      position: 'Organization',
      jobsPosted: 5,
    },
  },
  {
    email: '<EMAIL>',
    password: 'password123',
    name: 'Stellar Dynamics Corporation',
    phone: '+233-50-789012',
    role: 'organisation',
    profile: {
      bio: 'Private space technology company specializing in small satellite development, launch services, and space-based solutions for commercial and government clients. Founded in 2018, we are pioneering affordable access to space for African organizations.',
      skills: ['Satellite Manufacturing', 'Launch Services', 'Space Engineering', 'Project Management', 'Business Development'],
      interests: ['Commercial Space', 'Small Satellites', 'Launch Technology', 'Space Economy', 'Innovation'],
      education: 'Private Corporation',
      experience: '5+ years in commercial space sector',
      location: 'Accra, Ghana',
      currentLevel: 'advanced',
      careerGoals: ['Expand African space industry', 'Provide affordable space access', 'Drive space commercialization'],
    },
    preferences: {
      notifications: {
        email: true,
        sms: true,
        push: true,
      },
      language: 'en',
      timezone: 'Africa/Accra',
    },
    progress: {
      coursesCompleted: 0,
      certificatesEarned: 0,
      skillsAcquired: [],
      totalLearningHours: 0,
    },
    mentorship: {
      isMentor: false,
      isMentee: false,
      isApproved: false,
      expertise: [],
      mentees: [],
      mentors: [],
    },
    employment: {
      isEmployer: true,
      company: 'Stellar Dynamics Corporation',
      position: 'Organization',
      jobsPosted: 8,
    },
  },
  {
    email: '<EMAIL>',
    password: 'password123',
    name: 'Continental Space Alliance',
    phone: '+27-11-456789',
    role: 'organisation',
    profile: {
      bio: 'Pan-African organization promoting space cooperation, policy development, and capacity building across the continent. We work with governments, institutions, and private sector to advance Africa\'s space agenda through collaborative programs and initiatives.',
      skills: ['Policy Development', 'International Cooperation', 'Capacity Building', 'Program Management', 'Strategic Planning'],
      interests: ['Space Policy', 'International Relations', 'Capacity Building', 'African Unity', 'Sustainable Development'],
      education: 'International Organization',
      experience: '10+ years in space policy and cooperation',
      location: 'Johannesburg, South Africa',
      currentLevel: 'advanced',
      careerGoals: ['Unite African space efforts', 'Develop space policies', 'Foster continental cooperation'],
    },
    preferences: {
      notifications: {
        email: true,
        sms: true,
        push: false,
      },
      language: 'en',
      timezone: 'Africa/Johannesburg',
    },
    progress: {
      coursesCompleted: 0,
      certificatesEarned: 0,
      skillsAcquired: [],
      totalLearningHours: 0,
    },
    mentorship: {
      isMentor: false,
      isMentee: false,
      isApproved: false,
      expertise: [],
      mentees: [],
      mentors: [],
    },
    employment: {
      isEmployer: true,
      company: 'Continental Space Alliance',
      position: 'Organization',
      jobsPosted: 4,
    },
  },
];

// Mentor Users
export const sampleMentors = [
  {
    email: '<EMAIL>',
    password: 'password123',
    name: 'Dr. Amara Okafor',
    phone: '+254-**********',
    role: 'mentor',
    profile: {
      bio: 'Satellite systems engineer with 15 years of experience in designing and deploying Earth observation satellites. Currently leading the Satellite Engineering Department at African Space Research Institute. Expert in orbital mechanics, satellite subsystems, and mission planning.',
      skills: ['Satellite Engineering', 'Orbital Mechanics', 'Systems Design', 'Mission Planning', 'Earth Observation', 'MATLAB/Simulink'],
      interests: ['Space Technology', 'Education', 'Mentorship', 'African Space Development', 'Women in STEM'],
      education: 'PhD in Aerospace Engineering from University of Cape Town, MSc in Satellite Technology from Surrey Space Centre',
      experience: '15+ years in satellite systems engineering, 5+ years in mentorship',
      location: 'Nairobi, Kenya',
      currentLevel: 'advanced',
      careerGoals: ['Advance satellite technology in Africa', 'Mentor next generation engineers', 'Promote women in space'],
    },
    preferences: {
      notifications: {
        email: true,
        sms: true,
        push: true,
      },
      language: 'en',
      timezone: 'Africa/Nairobi',
    },
    progress: {
      coursesCompleted: 5,
      certificatesEarned: 3,
      skillsAcquired: ['Satellite Engineering', 'Project Management', 'Leadership'],
      totalLearningHours: 120,
    },
    mentorship: {
      isMentor: true,
      isMentee: false,
      isApproved: true,
      expertise: ['Satellite Engineering', 'Orbital Mechanics', 'Mission Design'],
      mentees: [],
      mentors: [],
    },
    employment: {
      isEmployer: false,
      company: 'African Space Research Institute',
      position: 'Head of Satellite Engineering',
      jobsPosted: 0,
    },
  },
  {
    email: '<EMAIL>',
    password: 'password123',
    name: 'Prof. Kwame Asante',
    phone: '+233-24-567890',
    role: 'mentor',
    profile: {
      bio: 'AI/ML researcher and professor specializing in space applications of artificial intelligence. Lead AI scientist at Stellar Dynamics Corporation with expertise in satellite data analysis, autonomous space systems, and machine learning for space missions.',
      skills: ['Artificial Intelligence', 'Machine Learning', 'Python', 'TensorFlow', 'Satellite Data Analysis', 'Computer Vision', 'Deep Learning'],
      interests: ['AI in Space', 'Machine Learning', 'Data Science', 'Research', 'Innovation', 'Technology Transfer'],
      education: 'PhD in Computer Science from MIT, MSc in AI from Stanford University',
      experience: '12+ years in AI research, 8+ years in space applications',
      location: 'Accra, Ghana',
      currentLevel: 'advanced',
      careerGoals: ['Advance AI applications in space', 'Bridge academia and industry', 'Develop African AI talent'],
    },
    preferences: {
      notifications: {
        email: true,
        sms: true,
        push: true,
      },
      language: 'en',
      timezone: 'Africa/Accra',
    },
    progress: {
      coursesCompleted: 8,
      certificatesEarned: 5,
      skillsAcquired: ['Artificial Intelligence', 'Machine Learning', 'Python', 'Research'],
      totalLearningHours: 200,
    },
    mentorship: {
      isMentor: true,
      isMentee: false,
      isApproved: true,
      expertise: ['Artificial Intelligence', 'Machine Learning', 'Satellite Data Analysis'],
      mentees: [],
      mentors: [],
    },
    employment: {
      isEmployer: false,
      company: 'Stellar Dynamics Corporation',
      position: 'Lead AI Scientist',
      jobsPosted: 0,
    },
  },
  {
    email: '<EMAIL>',
    password: 'password123',
    name: 'Dr. Fatima Al-Rashid',
    phone: '+27-82-123456',
    role: 'mentor',
    profile: {
      bio: 'Cybersecurity expert and space law specialist with extensive experience in protecting space infrastructure and developing space governance frameworks. Senior advisor at Continental Space Alliance focusing on space security and international space law.',
      skills: ['Cybersecurity', 'Space Law', 'Information Security', 'Risk Assessment', 'Policy Development', 'International Relations'],
      interests: ['Space Security', 'International Law', 'Cybersecurity', 'Policy Development', 'Space Governance', 'Ethics in Space'],
      education: 'PhD in International Law from Oxford University, MSc in Cybersecurity from Imperial College London',
      experience: '10+ years in cybersecurity, 8+ years in space law and policy',
      location: 'Johannesburg, South Africa',
      currentLevel: 'advanced',
      careerGoals: ['Secure space infrastructure', 'Develop space governance', 'Promote responsible space activities'],
    },
    preferences: {
      notifications: {
        email: true,
        sms: true,
        push: false,
      },
      language: 'en',
      timezone: 'Africa/Johannesburg',
    },
    progress: {
      coursesCompleted: 6,
      certificatesEarned: 4,
      skillsAcquired: ['Cybersecurity', 'Space Law', 'Policy Development', 'International Relations'],
      totalLearningHours: 150,
    },
    mentorship: {
      isMentor: true,
      isMentee: false,
      isApproved: true,
      expertise: ['Cybersecurity', 'Space Law', 'International Relations'],
      mentees: [],
      mentors: [],
    },
    employment: {
      isEmployer: false,
      company: 'Continental Space Alliance',
      position: 'Senior Space Policy Advisor',
      jobsPosted: 0,
    },
  },
];

export const sampleUsers = [...sampleOrganizations, ...sampleMentors];

// Events for each organization
export const sampleEvents = [
  // African Space Research Institute Events
  {
    title: 'African Satellite Technology Summit 2024',
    description: 'Join leading experts from across Africa for a comprehensive summit on satellite technology advancements, applications, and future opportunities. This three-day event will feature keynote presentations, technical workshops, and networking sessions focused on advancing Africa\'s space capabilities.',
    type: 'conference',
    category: 'Space Technology',
    organizer: {
      name: 'African Space Research Institute',
      organization: 'African Space Research Institute',
      email: '<EMAIL>',
      userId: null, // Will be set when we create users
    },
    speakers: [
      {
        name: 'Dr. Amara Okafor',
        title: 'Head of Satellite Engineering',
        organization: 'African Space Research Institute',
        bio: 'Leading satellite systems engineer with 15+ years of experience',
        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150',
      },
      {
        name: 'Prof. Sarah Mwangi',
        title: 'Director of Space Sciences',
        organization: 'University of Nairobi',
        bio: 'Renowned astrophysicist and space science educator',
        avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150',
      },
    ],
    schedule: {
      startDate: new Date('2024-03-15T09:00:00Z'),
      endDate: new Date('2024-03-17T17:00:00Z'),
      timezone: 'Africa/Nairobi',
    },
    location: {
      type: 'hybrid',
      venue: 'Kenyatta International Conference Centre',
      address: 'Harambee Avenue, Nairobi, Kenya',
      city: 'Nairobi',
      country: 'Kenya',
      meetingLink: 'https://zoom.us/j/asri-summit-2024',
    },
    registration: {
      isRequired: true,
      maxAttendees: 500,
      currentAttendees: 287,
      deadline: new Date('2024-03-10T23:59:59Z'),
      fee: 200,
      currency: 'USD',
      isFree: false,
      requiresApproval: false,
    },
    tags: ['satellite technology', 'conference', 'networking', 'africa'],
    status: 'published',
    visibility: 'public',
    featured: true,
    analytics: {
      views: 1250,
      registrations: 287,
      attendanceRate: 0,
      averageRating: 0,
      totalRatings: 0,
    },
    isRecurring: true,
    recurringPattern: {
      frequency: 'monthly',
      interval: 12,
    },
  },
  {
    title: 'Earth Observation Data Analysis Workshop',
    description: 'Hands-on workshop focusing on processing and analyzing satellite Earth observation data for environmental monitoring, agriculture, and urban planning applications. Participants will learn to use Python, QGIS, and Google Earth Engine.',
    type: 'workshop',
    category: 'Data Science',
    organizer: {
      name: 'African Space Research Institute',
      organization: 'African Space Research Institute',
      email: '<EMAIL>',
      userId: null,
    },
    speakers: [
      {
        name: 'Dr. James Kimani',
        title: 'Remote Sensing Specialist',
        organization: 'African Space Research Institute',
        bio: 'Expert in satellite data analysis and environmental monitoring',
        avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150',
      },
    ],
    schedule: {
      startDate: new Date('2024-02-20T08:00:00Z'),
      endDate: new Date('2024-02-22T16:00:00Z'),
      timezone: 'Africa/Nairobi',
    },
    location: {
      type: 'in_person',
      venue: 'ASRI Training Center',
      address: 'University Way, Nairobi, Kenya',
      city: 'Nairobi',
      country: 'Kenya',
    },
    registration: {
      isRequired: true,
      maxAttendees: 30,
      currentAttendees: 28,
      deadline: new Date('2024-02-15T23:59:59Z'),
      fee: 300,
      currency: 'USD',
      isFree: false,
      requiresApproval: true,
    },
    tags: ['earth observation', 'data analysis', 'python', 'workshop'],
    status: 'published',
    visibility: 'public',
    featured: false,
    analytics: {
      views: 450,
      registrations: 28,
      attendanceRate: 0,
      averageRating: 0,
      totalRatings: 0,
    },
    isRecurring: false,
  },
];

// Simplified data for testing - empty arrays by default
export const sampleResources: any[] = [];
export const sampleMentorProfiles: any[] = [];

/**
 * Create sample resources data when needed
 * This function can be called to populate the sampleResources array
 */
export function createSampleResources(users: any[]): any[] {
  // This would contain actual resource data when needed
  // For now, return empty array to maintain current behavior
  return [];
}

/**
 * Create sample mentor profiles data when needed
 * This function can be called to populate the sampleMentorProfiles array
 */
export function createSampleMentorProfiles(users: any[]): any[] {
  // This would contain actual mentor profile data when needed
  // For now, return empty array to maintain current behavior
  return [];
}

export async function seedDatabase(customConfig?: Partial<SeedConfig>) {
  try {
    await connectDB();

    // Get configuration
    const config = customConfig ? { ...getEnvironmentConfig(), ...customConfig } : getEnvironmentConfig();

    console.log('🌱 Seeding database...');
    logSeedConfig(config);

    // Clear existing data
    await User.deleteMany({});
    if (!config.skipCourses) {
      await Course.deleteMany({});
    }
    if (!config.skipJobs) {
      await Job.deleteMany({});
    }
    if (!config.skipEvents) {
      await Event.deleteMany({});
    }
    if (!config.skipResources) {
      await Resource.deleteMany({});
    }
    if (!config.skipMentorProfiles) {
      await MentorProfile.deleteMany({});
    }

    console.log('Creating users...');
    // Create users first
    const hashedPassword = await bcrypt.hash('password123', 12);
    const users = await User.insertMany(
      sampleUsers.map(user => ({
        ...user,
        password: hashedPassword,
      }))
    );

    console.log(`Created ${users.length} users`);

    // Map users by email for easy reference
    const userMap = new Map();
    users.forEach(user => {
      userMap.set(user.email, user);
    });

    // Get organization and mentor users
    const asriUser = userMap.get('<EMAIL>');
    const stellarUser = userMap.get('<EMAIL>');
    const csaUser = userMap.get('<EMAIL>');
    const amaraUser = userMap.get('<EMAIL>');
    const kwameUser = userMap.get('<EMAIL>');
    const fatimaUser = userMap.get('<EMAIL>');

    let coursesWithInstructors: any[] = [];
    if (!config.skipCourses) {
      console.log('Creating courses...');
      // Create courses with instructor IDs
      coursesWithInstructors = sampleCourses.map((course, index) => {
        let instructorId;
        if (course.instructor === 'Dr. Amara Okafor') {
          instructorId = amaraUser._id;
        } else if (course.instructor === 'Prof. Kwame Asante') {
          instructorId = kwameUser._id;
        } else if (course.instructor === 'Dr. Fatima Al-Rashid') {
          instructorId = fatimaUser._id;
        } else {
          instructorId = amaraUser._id; // Default to Amara
        }

        return {
          ...course,
          instructorId,
        };
      });

      await Course.insertMany(coursesWithInstructors);
      console.log(`Created ${coursesWithInstructors.length} courses`);
    } else {
      console.log('⏭️  Skipping courses creation');
    }

    let jobsWithEmployers: any[] = [];
    if (!config.skipJobs) {
      console.log('Creating jobs...');
      // Create jobs with employer IDs
      jobsWithEmployers = sampleJobs.map(job => {
        let employerId;
        if (job.company === 'African Space Research Institute') {
          employerId = asriUser._id;
        } else if (job.company === 'Stellar Dynamics Corporation') {
          employerId = stellarUser._id;
        } else if (job.company === 'Continental Space Alliance') {
          employerId = csaUser._id;
        } else {
          employerId = asriUser._id; // Default
        }

        return {
          ...job,
          employerId,
        };
      });

      await Job.insertMany(jobsWithEmployers);
      console.log(`Created ${jobsWithEmployers.length} jobs`);
    } else {
      console.log('⏭️  Skipping jobs creation');
    }

    let eventsWithOrganizers: any[] = [];
    if (!config.skipEvents) {
      console.log('Creating events...');
      // Create events with organizer IDs
      eventsWithOrganizers = sampleEvents.map(event => {
        let organizerUserId;
        if (event.organizer.organization === 'African Space Research Institute') {
          organizerUserId = asriUser._id;
        } else if (event.organizer.organization === 'Stellar Dynamics Corporation') {
          organizerUserId = stellarUser._id;
        } else if (event.organizer.organization === 'Continental Space Alliance') {
          organizerUserId = csaUser._id;
        } else {
          organizerUserId = asriUser._id; // Default
        }

        return {
          ...event,
          organizer: {
            ...event.organizer,
            userId: organizerUserId,
          },
        };
      });

      await Event.insertMany(eventsWithOrganizers);
      console.log(`Created ${eventsWithOrganizers.length} events`);
    } else {
      console.log('⏭️  Skipping events creation');
    }

    // Handle resources creation
    if (!config.skipResources && sampleResources.length > 0) {
      console.log('Creating resources...');
      await Resource.insertMany(sampleResources);
      console.log(`Created ${sampleResources.length} resources`);
    } else {
      console.log('⏭️  Skipping resources creation (empty array or disabled)');
    }

    // Handle mentor profiles creation
    if (!config.skipMentorProfiles && sampleMentorProfiles.length > 0) {
      console.log('Creating mentor profiles...');
      await MentorProfile.insertMany(sampleMentorProfiles);
      console.log(`Created ${sampleMentorProfiles.length} mentor profiles`);
    } else {
      console.log('⏭️  Skipping mentor profiles creation (empty array or disabled)');
    }

    console.log('\n🎉 Database seeded successfully!');
    console.log('\n=== SAMPLE LOGIN CREDENTIALS ===');
    console.log('\n--- ORGANIZATIONS ---');
    console.log('African Space Research Institute: <EMAIL> / password123');
    console.log('Stellar Dynamics Corporation: <EMAIL> / password123');
    console.log('Continental Space Alliance: <EMAIL> / password123');
    console.log('\n--- MENTORS ---');
    console.log('Dr. Amara Okafor (Satellite Engineering): <EMAIL> / password123');
    console.log('Prof. Kwame Asante (AI/ML): <EMAIL> / password123');
    console.log('Dr. Fatima Al-Rashid (Cybersecurity/Law): <EMAIL> / password123');
    console.log('\n=== SUMMARY ===');
    console.log(`✓ ${users.length} users created (3 organizations + 3 mentors)`);
    console.log(`✓ ${coursesWithInstructors.length} courses ${config.skipCourses ? 'skipped' : 'created'}`);
    console.log(`✓ ${jobsWithEmployers.length} job postings ${config.skipJobs ? 'skipped' : 'created'}`);
    console.log(`✓ ${eventsWithOrganizers.length} events ${config.skipEvents ? 'skipped' : 'created'}`);
    console.log(`✓ Resources ${config.skipResources ? 'skipped' : `created (${sampleResources.length})`}`);
    console.log(`✓ Mentor profiles ${config.skipMentorProfiles ? 'skipped' : `created (${sampleMentorProfiles.length})`}`);

  } catch (error) {
    console.error('Error seeding database:', error);
    throw error;
  }
}
