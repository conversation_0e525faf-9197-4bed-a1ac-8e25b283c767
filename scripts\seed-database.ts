#!/usr/bin/env tsx

import { config } from 'dotenv';
import path from 'path';
import { seedDatabase } from '../src/lib/seed-data';

// Load environment variables from .env.local
config({ path: path.resolve(process.cwd(), '.env.local') });

async function runSeeding() {
  console.log('🌱 Starting database seeding...');
  console.log('📍 Environment:', process.env.NODE_ENV || 'development');
  
  if (!process.env.MONGODB_URI) {
    console.error('❌ MONGODB_URI environment variable is required');
    process.exit(1);
  }

  try {
    await seedDatabase();
    console.log('\n🎉 Database seeding completed successfully!');
    console.log('\n📝 Next steps:');
    console.log('1. Start the development server: pnpm dev');
    console.log('2. Visit http://localhost:3000');
    console.log('3. Login with any of the provided credentials');
    console.log('4. Explore the platform features!');
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Error seeding database:', error);
    process.exit(1);
  }
}

// Run the seeding
runSeeding().catch(console.error);
