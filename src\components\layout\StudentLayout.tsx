'use client';

import React from 'react';
import { StudentNavbar } from './StudentNavbar';

interface StudentLayoutProps {
  children: React.ReactNode;
  coursesEnrolled?: number;
  pendingApplications?: number;
  upcomingSessions?: number;
}

export function StudentLayout({ 
  children, 
  coursesEnrolled = 0, 
  pendingApplications = 0,
  upcomingSessions = 0 
}: StudentLayoutProps) {
  return (
    <div className="min-h-screen bg-gray-50">
      <StudentNavbar 
        coursesEnrolled={coursesEnrolled} 
        pendingApplications={pendingApplications}
        upcomingSessions={upcomingSessions}
      />
      
      {/* Main content */}
      <div className="lg:ml-64">
        <main className="p-4 lg:p-8">
          {children}
        </main>
      </div>
    </div>
  );
}
