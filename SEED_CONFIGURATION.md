# 🌱 Seed Data Configuration Guide

This guide explains the flexible seed data configuration system that allows you to control what data gets created during database initialization. This is particularly useful for testing scenarios where you want to skip certain resource-intensive or complex entity creation.

## 🎯 Overview

The seed configuration system provides multiple ways to control data creation:

- **Environment Variables**: Set persistent configuration in `.env.local`
- **Programmatic Configuration**: Pass configuration directly to the seed function
- **API Configuration**: Send configuration via the seed API endpoint
- **Predefined Modes**: Use built-in configurations for common scenarios

## 🔧 Configuration Options

### Available Settings

| Option | Type | Default | Description |
|--------|------|---------|-------------|
| `skipResources` | boolean | false | Skip creating resource documents |
| `skipMentorProfiles` | boolean | false | Skip creating mentor profile documents |
| `skipJobs` | boolean | false | Skip creating job postings |
| `skipEvents` | boolean | false | Skip creating events |
| `skipCourses` | boolean | false | Skip creating courses |
| `minimalMode` | boolean | false | Only create essential data (users + courses) |

### Environment Variables

```bash
# .env.local
SEED_SKIP_RESOURCES=true          # Skip resources
SEED_SKIP_MENTOR_PROFILES=true    # Skip mentor profiles
SEED_MINIMAL_MODE=true            # Minimal mode (overrides other settings)
```

## 🚀 Usage Examples

### 1. Command Line Scripts

```bash
# Full seeding (default)
pnpm seed

# Fast seeding (skip resources and mentor profiles)
pnpm seed:fast

# Minimal seeding (only users and courses)
pnpm seed:minimal

# Test all configurations
pnpm seed:test
```

### 2. Environment Variables

```bash
# Set in .env.local for persistent configuration
SEED_SKIP_RESOURCES=true
SEED_SKIP_MENTOR_PROFILES=true

# Then run normal seed command
pnpm seed
```

### 3. Programmatic Usage

```typescript
import { seedDatabase } from '@/lib/seed-data';

// Skip specific entities
await seedDatabase({
  skipResources: true,
  skipMentorProfiles: true
});

// Minimal mode
await seedDatabase({
  minimalMode: true
});

// Custom configuration
await seedDatabase({
  skipResources: true,
  skipJobs: false,
  skipEvents: true
});
```

### 4. API Configuration

```bash
# Basic seeding
curl -X POST http://localhost:3000/api/seed

# With configuration
curl -X POST http://localhost:3000/api/seed \
  -H "Content-Type: application/json" \
  -d '{
    "config": {
      "skipResources": true,
      "skipMentorProfiles": true
    }
  }'

# Minimal mode
curl -X POST http://localhost:3000/api/seed \
  -H "Content-Type: application/json" \
  -d '{
    "config": {
      "minimalMode": true
    }
  }'
```

## 🎭 Predefined Modes

### Development Mode (Default)
- Creates all entities
- Full dataset for comprehensive testing
- Best for feature development

### Testing Mode
```typescript
{
  skipResources: true,
  skipMentorProfiles: true,
  skipJobs: false,
  skipEvents: false,
  skipCourses: false
}
```

### Minimal Mode
```typescript
{
  skipResources: true,
  skipMentorProfiles: true,
  skipJobs: true,
  skipEvents: true,
  skipCourses: false  // Keep courses for basic functionality
}
```

## 🔍 What Gets Created

### Always Created
- **Users**: Organizations and mentors (essential for authentication)

### Conditionally Created
- **Courses**: Created unless `skipCourses` is true or `minimalMode` is false
- **Jobs**: Created unless `skipJobs` is true or `minimalMode` is true
- **Events**: Created unless `skipEvents` is true or `minimalMode` is true
- **Resources**: Created unless `skipResources` is true or `minimalMode` is true
- **Mentor Profiles**: Created unless `skipMentorProfiles` is true or `minimalMode` is true

## 🛡️ Safety & Compatibility

### API Compatibility
- All API endpoints handle empty collections gracefully
- Resources API returns empty arrays when no resources exist
- Mentor profiles API returns empty arrays when no profiles exist
- No breaking changes to existing functionality

### Database Integrity
- User relationships are always maintained
- Foreign key references are properly handled
- No orphaned data is created

### Error Handling
- Configuration validation prevents invalid settings
- Clear error messages for missing required data
- Graceful fallbacks for missing entities

## 🧪 Testing Scenarios

### Unit Tests
```typescript
// Fast test setup
await seedDatabase({ minimalMode: true });
```

### Integration Tests
```typescript
// Skip complex entities
await seedDatabase({
  skipResources: true,
  skipMentorProfiles: true
});
```

### Performance Tests
```typescript
// Minimal data for load testing
await seedDatabase({ minimalMode: true });
```

## 📊 Performance Impact

| Mode | Users | Courses | Jobs | Events | Resources | Profiles | Time |
|------|-------|---------|------|--------|-----------|----------|------|
| Full | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ | ~5s |
| Fast | ✅ | ✅ | ✅ | ✅ | ❌ | ❌ | ~3s |
| Minimal | ✅ | ✅ | ❌ | ❌ | ❌ | ❌ | ~1s |

## 🔄 Migration Guide

### From Previous Version
The new configuration system is backward compatible. Existing seed scripts will continue to work without changes.

### Updating Tests
```typescript
// Before
await seedDatabase();

// After (for faster tests)
await seedDatabase({ skipResources: true, skipMentorProfiles: true });
```

## 🐛 Troubleshooting

### Common Issues

1. **Empty mentor list**: If `skipMentorProfiles` is true, mentor profiles won't be created
2. **No resources available**: If `skipResources` is true, resource library will be empty
3. **Configuration not applied**: Check environment variable names and values

### Debug Mode
```bash
# Enable detailed logging
DEBUG=seed pnpm seed
```

## 📝 Best Practices

1. **Use minimal mode for unit tests** - Fastest setup time
2. **Use fast mode for integration tests** - Good balance of speed and functionality
3. **Use full mode for manual testing** - Complete feature testing
4. **Set environment variables for persistent configuration** - Avoid repeating configuration
5. **Document test requirements** - Specify which entities your tests need
