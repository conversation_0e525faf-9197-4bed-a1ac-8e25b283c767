'use client';

import React, { useState, useEffect } from 'react';
import { StudentLayout } from '@/components/layout/StudentLayout';

interface StudentDashboardLayoutProps {
  children: React.ReactNode;
}

export default function StudentDashboardLayout({ children }: StudentDashboardLayoutProps) {
  const [coursesEnrolled, setCoursesEnrolled] = useState(0);
  const [pendingApplications, setPendingApplications] = useState(0);
  const [upcomingSessions, setUpcomingSessions] = useState(0);

  useEffect(() => {
    // Fetch notification counts for the navbar
    const fetchNotificationCounts = async () => {
      try {
        const [statsRes] = await Promise.all([
          fetch('/api/dashboard/student/stats'),
        ]);

        if (statsRes.ok) {
          const statsData = await statsRes.json();
          setCoursesEnrolled(statsData.data?.coursesEnrolled || 0);
          setPendingApplications(statsData.data?.jobApplications || 0);
          setUpcomingSessions(statsData.data?.mentorshipSessions || 0);
        }
      } catch (error) {
        console.error('Failed to fetch notification counts:', error);
      }
    };

    fetchNotificationCounts();
    
    // Refresh counts every 30 seconds
    const interval = setInterval(fetchNotificationCounts, 30000);
    
    return () => clearInterval(interval);
  }, []);

  return (
    <StudentLayout 
      coursesEnrolled={coursesEnrolled} 
      pendingApplications={pendingApplications}
      upcomingSessions={upcomingSessions}
    >
      {children}
    </StudentLayout>
  );
}
