import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { connectDB } from '@/lib/mongodb';
import { MentorshipSession } from '@/models/Mentorship';
import { User } from '@/models/User';
import { SMSService } from '@/lib/sms';

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    await connectDB();

    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');
    const role = searchParams.get('role'); // 'mentor' or 'mentee'
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const skip = (page - 1) * limit;

    // Build query
    const query: any = {};
    
    if (role === 'mentor') {
      query.mentorId = session.user.id;
    } else if (role === 'mentee') {
      query.menteeId = session.user.id;
    } else {
      // Show both mentor and mentee sessions
      query.$or = [
        { mentorId: session.user.id },
        { menteeId: session.user.id }
      ];
    }

    if (status) {
      query.status = status;
    }

    const sessions = await MentorshipSession.find(query)
      .populate('mentorId', 'name email avatar')
      .populate('menteeId', 'name email avatar')
      .sort({ scheduledAt: -1 })
      .skip(skip)
      .limit(limit);

    const total = await MentorshipSession.countDocuments(query);

    return NextResponse.json({
      success: true,
      data: {
        sessions,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      },
    });
  } catch (error) {
    console.error('Error fetching mentorship sessions:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch sessions' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    await connectDB();

    const body = await request.json();
    const {
      mentorId,
      title,
      description,
      scheduledAt,
      duration = 60,
      meetingType = 'video',
      price,
      currency = 'USD',
    } = body;

    // Validate required fields
    if (!mentorId || !title || !scheduledAt) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Validate scheduled time is in the future
    const scheduledDate = new Date(scheduledAt);
    if (scheduledDate <= new Date()) {
      return NextResponse.json(
        { success: false, error: 'Scheduled time must be in the future' },
        { status: 400 }
      );
    }

    // Check if mentor exists
    const mentor = await User.findById(mentorId);
    if (!mentor) {
      return NextResponse.json(
        { success: false, error: 'Mentor not found' },
        { status: 404 }
      );
    }

    // Check for scheduling conflicts
    const conflictingSession = await MentorshipSession.findOne({
      $or: [
        { mentorId, scheduledAt: { $gte: scheduledDate, $lt: new Date(scheduledDate.getTime() + duration * 60000) } },
        { menteeId: session.user.id, scheduledAt: { $gte: scheduledDate, $lt: new Date(scheduledDate.getTime() + duration * 60000) } }
      ],
      status: { $in: ['scheduled', 'in_progress'] }
    });

    if (conflictingSession) {
      return NextResponse.json(
        { success: false, error: 'Time slot conflicts with existing session' },
        { status: 409 }
      );
    }

    // Create session
    const newSession = new MentorshipSession({
      mentorId,
      menteeId: session.user.id,
      title,
      description,
      scheduledAt: scheduledDate,
      duration,
      meetingType,
      price: price || 0,
      currency,
      isPaid: price > 0,
      paymentStatus: price > 0 ? 'pending' : undefined,
    });

    await newSession.save();

    // Populate the session with user details
    await newSession.populate('mentorId', 'name email phone');
    await newSession.populate('menteeId', 'name email phone');

    // Send SMS notifications
    try {
      const mentorUser = newSession.mentorId as any;
      const menteeUser = newSession.menteeId as any;

      if (mentorUser.phone) {
        await SMSService.sendSMS(
          mentorUser.phone,
          'mentorship_booking',
          {
            mentorName: mentorUser.name,
            menteeName: menteeUser.name,
            sessionTitle: title,
            scheduledAt: scheduledDate.toLocaleString(),
            duration: `${duration} minutes`,
          }
        );
      }

      if (menteeUser.phone) {
        await SMSService.sendSMS(
          menteeUser.phone,
          'mentorship_booking',
          {
            mentorName: mentorUser.name,
            menteeName: menteeUser.name,
            sessionTitle: title,
            scheduledAt: scheduledDate.toLocaleString(),
            duration: `${duration} minutes`,
          }
        );
      }
    } catch (smsError) {
      console.error('Error sending SMS notifications:', smsError);
      // Don't fail the request if SMS fails
    }

    return NextResponse.json({
      success: true,
      data: newSession,
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating mentorship session:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to create session' },
      { status: 500 }
    );
  }
}
