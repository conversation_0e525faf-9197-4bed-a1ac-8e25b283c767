'use client';

import React from 'react';
import { useSession } from 'next-auth/react';
import { UserRole, PermissionChecker } from '@/lib/permissions';
import { createPermissionChecker } from '@/lib/client-auth-utils';

interface RoleGuardProps {
  children: React.ReactNode;
  allowedRoles?: UserRole[];
  requiredPermissions?: string[];
  requireAll?: boolean; // If true, user must have ALL permissions; if false, ANY permission
  fallback?: React.ReactNode;
  showFallback?: boolean;
}

interface PermissionGuardProps {
  children: React.ReactNode;
  permission: string;
  fallback?: React.ReactNode;
  showFallback?: boolean;
}

interface ConditionalRenderProps {
  children: React.ReactNode;
  condition: boolean;
  fallback?: React.ReactNode;
}

// Main role guard component
export function RoleGuard({
  children,
  allowedRoles,
  requiredPermissions,
  requireAll = true,
  fallback = null,
  showFallback = true,
}: RoleGuardProps) {
  const { data: session, status } = useSession();

  // Loading state
  if (status === 'loading') {
    return (
      <div className="flex items-center justify-center p-4">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  // Not authenticated
  if (!session?.user) {
    return showFallback ? (
      fallback || (
        <div className="text-center p-4">
          <p className="text-gray-600">Please sign in to access this content.</p>
        </div>
      )
    ) : null;
  }

  const userRole = session.user.role as UserRole;
  const permissionChecker = createPermissionChecker(userRole);

  // Check role-based access
  if (allowedRoles && !allowedRoles.includes(userRole)) {
    return showFallback ? (
      fallback || (
        <div className="text-center p-4">
          <p className="text-red-600">You don't have permission to access this content.</p>
        </div>
      )
    ) : null;
  }

  // Check permission-based access
  if (requiredPermissions && requiredPermissions.length > 0) {
    const hasAccess = requireAll
      ? permissionChecker.canAll(requiredPermissions)
      : permissionChecker.canAny(requiredPermissions);

    if (!hasAccess) {
      return showFallback ? (
        fallback || (
          <div className="text-center p-4">
            <p className="text-red-600">You don't have the required permissions.</p>
          </div>
        )
      ) : null;
    }
  }

  return <>{children}</>;
}

// Permission-specific guard
export function PermissionGuard({
  children,
  permission,
  fallback = null,
  showFallback = true,
}: PermissionGuardProps) {
  return (
    <RoleGuard
      requiredPermissions={[permission]}
      fallback={fallback}
      showFallback={showFallback}
    >
      {children}
    </RoleGuard>
  );
}

// Conditional render based on role
export function RoleConditional({
  children,
  condition,
  fallback,
}: ConditionalRenderProps) {
  return condition ? <>{children}</> : <>{fallback}</>;
}

// Hook for role-based logic
export function useRoleAccess() {
  const { data: session } = useSession();

  if (!session?.user?.role) {
    return {
      userRole: null,
      isAuthenticated: false,
      can: () => false,
      canAny: () => false,
      canAll: () => false,
      is: () => false,
      isAny: () => false,
    };
  }

  const userRole = session.user.role as UserRole;
  const permissionChecker = createPermissionChecker(userRole);

  return {
    userRole,
    isAuthenticated: true,
    ...permissionChecker,
  };
}

// Role-specific components
export function AdminOnly({ children, fallback }: { children: React.ReactNode; fallback?: React.ReactNode }) {
  return (
    <RoleGuard allowedRoles={['admin']} fallback={fallback}>
      {children}
    </RoleGuard>
  );
}

export function MentorOnly({ children, fallback }: { children: React.ReactNode; fallback?: React.ReactNode }) {
  return (
    <RoleGuard allowedRoles={['mentor']} fallback={fallback}>
      {children}
    </RoleGuard>
  );
}

export function OrganisationOnly({ children, fallback }: { children: React.ReactNode; fallback?: React.ReactNode }) {
  return (
    <RoleGuard allowedRoles={['organisation']} fallback={fallback}>
      {children}
    </RoleGuard>
  );
}

export function MentorOrAdmin({ children, fallback }: { children: React.ReactNode; fallback?: React.ReactNode }) {
  return (
    <RoleGuard allowedRoles={['mentor', 'admin']} fallback={fallback}>
      {children}
    </RoleGuard>
  );
}

export function OrganisationOrAdmin({ children, fallback }: { children: React.ReactNode; fallback?: React.ReactNode }) {
  return (
    <RoleGuard allowedRoles={['organisation', 'admin']} fallback={fallback}>
      {children}
    </RoleGuard>
  );
}

// Role badge component
export function RoleBadge({ role, className = '' }: { role: UserRole; className?: string }) {
  const roleColors = {
    admin: 'bg-red-100 text-red-800 border-red-200',
    organisation: 'bg-purple-100 text-purple-800 border-purple-200',
    mentor: 'bg-blue-100 text-blue-800 border-blue-200',
    student: 'bg-gray-100 text-gray-800 border-gray-200',
  };

  const roleLabels = {
    admin: 'Administrator',
    organisation: 'Organization',
    mentor: 'Mentor',
    student: 'Student',
  };

  return (
    <span
      className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${
        roleColors[role]
      } ${className}`}
    >
      {roleLabels[role]}
    </span>
  );
}

// Permission list component for debugging/admin
export function PermissionsList({ userRole }: { userRole: UserRole }) {
  const permissions = PermissionChecker.getPermissionsForRole(userRole);

  return (
    <div className="bg-gray-50 p-4 rounded-lg">
      <h3 className="text-lg font-semibold mb-2">Permissions for {userRole}</h3>
      <ul className="space-y-1">
        {permissions.map((permission) => (
          <li key={permission} className="text-sm text-gray-600">
            • {permission}
          </li>
        ))}
      </ul>
    </div>
  );
}

export default RoleGuard;
