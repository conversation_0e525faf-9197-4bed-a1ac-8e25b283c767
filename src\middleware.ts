import { withAuth } from 'next-auth/middleware';
import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

// Define public routes that don't require authentication
const publicRoutes = [
  '/',
  '/about',
  '/contact',
  '/courses',
  '/auth/signin',
  '/auth/signup',
  '/auth/forgot-password',
  '/auth/reset-password',
  '/api/auth',
  '/verify',
];

// Define role-specific dashboard routes
const roleDashboards = {
  admin: '/dashboard/admin',
  student: '/dashboard/student',
  mentor: '/dashboard/mentor',
  organisation: '/dashboard/organisation',
};

// Define protected routes and their required roles
const protectedRoutes = {
  '/dashboard/admin': ['admin'],
  '/dashboard/student': ['student'],
  '/dashboard/mentor': ['mentor'],
  '/dashboard/organisation': ['organisation'],
  '/mentorship': ['student', 'mentor', 'admin'],
  '/jobs': ['student', 'organisation', 'admin'],
  '/events': ['student', 'mentor', 'organisation', 'admin'],
  '/community': ['student', 'mentor', 'organisation', 'admin'],
  '/resources': ['student', 'mentor', 'organisation', 'admin'],
  '/certificates': ['student', 'mentor', 'admin'],
  '/assessments': ['student', 'mentor', 'admin'],
};

function isPublicRoute(pathname: string): boolean {
  return publicRoutes.some(route => {
    if (route === '/') return pathname === '/';
    return pathname.startsWith(route);
  });
}

function getRequiredRoles(pathname: string): string[] | null {
  // Check exact matches first
  if (protectedRoutes[pathname as keyof typeof protectedRoutes]) {
    return protectedRoutes[pathname as keyof typeof protectedRoutes];
  }

  // Check for partial matches (e.g., /dashboard/admin/users should match /dashboard/admin)
  for (const [route, roles] of Object.entries(protectedRoutes)) {
    if (pathname.startsWith(route)) {
      return roles;
    }
  }

  return null;
}

export default withAuth(
  function middleware(req: NextRequest & { nextauth: { token: any } }) {
    const { pathname } = req.nextUrl;
    const token = req.nextauth.token;

    // Allow public routes
    if (isPublicRoute(pathname)) {
      return NextResponse.next();
    }

    // Handle /dashboard route - redirect to role-specific dashboard
    if (pathname === '/dashboard') {
      if (token?.role) {
        const dashboardUrl = roleDashboards[token.role as keyof typeof roleDashboards];
        if (dashboardUrl) {
          return NextResponse.redirect(new URL(dashboardUrl, req.url));
        }
      }
      // If no role or unknown role, redirect to student dashboard as default
      return NextResponse.redirect(new URL('/dashboard/student', req.url));
    }

    // Check if route requires specific roles
    const requiredRoles = getRequiredRoles(pathname);
    if (requiredRoles) {
      const userRole = token?.role;

      if (!userRole || !requiredRoles.includes(userRole)) {
        // User doesn't have required role, redirect to their appropriate dashboard
        const userDashboard = roleDashboards[userRole as keyof typeof roleDashboards] || '/dashboard/student';
        return NextResponse.redirect(new URL(userDashboard, req.url));
      }
    }

    // Check if user is trying to access wrong dashboard
    for (const [role, dashboard] of Object.entries(roleDashboards)) {
      if (pathname.startsWith(dashboard) && token?.role !== role) {
        // Redirect to user's correct dashboard
        const correctDashboard = roleDashboards[token?.role as keyof typeof roleDashboards] || '/dashboard/student';
        return NextResponse.redirect(new URL(correctDashboard, req.url));
      }
    }

    return NextResponse.next();
  },
  {
    callbacks: {
      authorized: ({ token, req }) => {
        const { pathname } = req.nextUrl;

        // Allow public routes
        if (isPublicRoute(pathname)) {
          return true;
        }

        // Require authentication for all other routes
        return !!token;
      },
    },
  }
);

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder files
     */
    '/((?!api|_next/static|_next/image|favicon.ico|public).*)',
  ],
};
