'use client';

import React, { useState, useEffect } from 'react';
import { AdminLayout } from '@/components/layout/AdminLayout';

interface AdminDashboardLayoutProps {
  children: React.ReactNode;
}

export default function AdminDashboardLayout({ children }: AdminDashboardLayoutProps) {
  const [pendingApprovals, setPendingApprovals] = useState(0);
  const [systemAlerts, setSystemAlerts] = useState(0);

  useEffect(() => {
    // Fetch notification counts for the navbar
    const fetchNotificationCounts = async () => {
      try {
        const [approvalsRes, alertsRes] = await Promise.all([
          fetch('/api/admin/approvals?count=true'),
          fetch('/api/admin/alerts?count=true'),
        ]);

        if (approvalsRes.ok) {
          const approvalsData = await approvalsRes.json();
          setPendingApprovals(approvalsData.data?.count || 0);
        }

        if (alertsRes.ok) {
          const alertsData = await alertsRes.json();
          setSystemAlerts(alertsData.data?.count || 0);
        }
      } catch (error) {
        console.error('Failed to fetch notification counts:', error);
      }
    };

    fetchNotificationCounts();
    
    // Refresh counts every 30 seconds
    const interval = setInterval(fetchNotificationCounts, 30000);
    
    return () => clearInterval(interval);
  }, []);

  return (
    <AdminLayout pendingApprovals={pendingApprovals} systemAlerts={systemAlerts}>
      {children}
    </AdminLayout>
  );
}
