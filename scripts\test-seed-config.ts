#!/usr/bin/env tsx

import { config } from 'dotenv';
import path from 'path';
import { seedDatabase } from '../src/lib/seed-data';
import { getSeedConfig, logSeedConfig, TEST_CONFIG } from '../src/lib/seed-config';

// Load environment variables from .env.local
config({ path: path.resolve(process.cwd(), '.env.local') });

async function testSeedConfigurations() {
  console.log('🧪 Testing Seed Configuration Options...\n');
  
  if (!process.env.MONGODB_URI) {
    console.error('❌ MONGODB_URI environment variable is required');
    process.exit(1);
  }

  try {
    console.log('📋 Current Environment Configuration:');
    const envConfig = getSeedConfig();
    logSeedConfig(envConfig);

    console.log('📋 Test Configuration (for reference):');
    logSeedConfig(TEST_CONFIG);

    console.log('🚀 Testing with current environment configuration...');
    await seedDatabase();
    console.log('✅ Environment configuration test completed!\n');

    console.log('🚀 Testing with minimal mode override...');
    await seedDatabase({ minimalMode: true });
    console.log('✅ Minimal mode test completed!\n');

    console.log('🚀 Testing with skip resources and mentor profiles...');
    await seedDatabase({ 
      skipResources: true, 
      skipMentorProfiles: true 
    });
    console.log('✅ Skip configuration test completed!\n');

    console.log('🎉 All seed configuration tests completed successfully!');
    console.log('\n📝 Usage Examples:');
    console.log('1. Environment variables: Set SEED_SKIP_RESOURCES=true in .env.local');
    console.log('2. Programmatic: await seedDatabase({ skipResources: true })');
    console.log('3. API: POST /api/seed with { "config": { "skipResources": true } }');
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Error testing seed configurations:', error);
    process.exit(1);
  }
}

// Run the tests
testSeedConfigurations().catch(console.error);
