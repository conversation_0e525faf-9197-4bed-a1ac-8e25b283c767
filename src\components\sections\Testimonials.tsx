'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { Star, Quote } from 'lucide-react';

const testimonials = [
  {
    name: '<PERSON><PERSON>',
    role: 'Satellite Engineer',
    company: 'Kenya Space Agency',
    image: '/api/placeholder/64/64',
    content: '<PERSON> transformed my career. The mentorship program connected me with industry leaders, and the practical courses gave me the skills I needed to land my dream job in satellite engineering.',
    rating: 5,
  },
  {
    name: '<PERSON><PERSON><PERSON>',
    role: 'AI Research Scientist',
    company: 'Ghana Space Science & Technology Institute',
    image: '/api/placeholder/64/64',
    content: 'The AI in Space course was exceptional. The hands-on projects and real-world applications helped me transition from software development to space technology research.',
    rating: 5,
  },
  {
    name: '<PERSON><PERSON>',
    role: 'Cybersecurity Specialist',
    company: 'Egyptian Space Agency',
    image: '/api/placeholder/64/64',
    content: 'As someone new to space technology, Nova\'s beginner-friendly approach and supportive community made all the difference. I\'m now leading cybersecurity initiatives for satellite systems.',
    rating: 5,
  },
  {
    name: '<PERSON><PERSON><PERSON>',
    role: 'Space Entrepreneur',
    company: 'Founder, AstroTech Solutions',
    image: '/api/placeholder/64/64',
    content: 'The business and entrepreneurship courses on Nova helped me launch my space tech startup. The network I built through the platform has been invaluable for partnerships and funding.',
    rating: 5,
  },
  {
    name: 'Aisha Hassan',
    role: 'Data Scientist',
    company: 'Nigerian Space Research Centre',
    image: '/api/placeholder/64/64',
    content: 'Nova\'s data science courses with a focus on space applications opened up a whole new career path for me. The certification helped me stand out in the competitive job market.',
    rating: 5,
  },
  {
    name: 'Jean-Baptiste Uwimana',
    role: 'Robotics Engineer',
    company: 'Rwanda Space Agency',
    image: '/api/placeholder/64/64',
    content: 'The robotics and automation courses were incredibly detailed and practical. I\'ve applied what I learned to develop robotic systems for satellite deployment and maintenance.',
    rating: 5,
  },
];

export function Testimonials() {
  return (
    <section className="py-24 relative overflow-hidden">
      {/* Muted Galaxy Background */}
      <div className="absolute inset-0">
        {/* Floating cosmic elements with reduced opacity */}
        <div className="absolute top-32 right-1/4 w-72 h-72 bg-gradient-to-r from-blue-600/4 to-purple-600/4 rounded-full filter blur-3xl animate-cosmic-drift" />
        <div className="absolute bottom-32 left-1/4 w-56 h-56 bg-gradient-to-r from-pink-600/4 to-blue-600/4 rounded-full filter blur-3xl animate-cosmic-drift animation-delay-2000" />

        {/* Twinkling stars */}
        <div className="absolute top-24 left-1/3 w-1 h-1 bg-blue-400 rounded-full animate-twinkle" />
        <div className="absolute bottom-40 right-1/3 w-1.5 h-1.5 bg-pink-400 rounded-full animate-twinkle animation-delay-1000" />
        <div className="absolute top-48 right-1/4 w-1 h-1 bg-purple-400 rounded-full animate-twinkle animation-delay-3000" />
        <div className="absolute bottom-64 left-1/2 w-1 h-1 bg-blue-300 rounded-full animate-twinkle animation-delay-4000" />
      </div>

      <div className="relative section-container">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-20"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6 font-orbitron">
            Success Stories from
            <span className="galaxy-text-gradient">
              {" "}Our Cosmic Community
            </span>
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
            Hear from space explorers who have transformed their careers through Nova's comprehensive
            cosmic technology education platform across the galaxy.
          </p>
        </motion.div>

        {/* Testimonials Bento Grid */}
        <div className="bento-grid bento-grid-testimonials">
          {testimonials.map((testimonial, index) => {
            const isTall = index === 1 || index === 4; // Make second and fifth items taller
            const isWide = index === 0; // Make first item wide

            return (
              <motion.div
                key={testimonial.name}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className={`bento-item muted-glassmorphic hover:bg-white/3 transition-all duration-300 flex flex-col group ${
                  isTall ? 'bento-item-tall' : ''
                } ${isWide ? 'bento-item-wide' : ''}`}
              >
                {/* Quote Icon */}
                <div className="flex justify-between items-start mb-4">
                  <Quote className="h-6 w-6 text-blue-400 opacity-70" />
                  <div className="flex space-x-1">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Star key={i} className="h-3 w-3 text-purple-400 fill-current" />
                    ))}
                  </div>
                </div>

                {/* Content */}
                <blockquote className="text-gray-300 leading-relaxed mb-4 flex-grow text-sm">
                  "{testimonial.content}"
                </blockquote>

                {/* Author */}
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-gradient-to-br from-blue-600 to-pink-600 rounded-full flex items-center justify-center text-white font-semibold text-sm">
                    {testimonial.name.split(' ').map(n => n[0]).join('')}
                  </div>
                  <div>
                    <div className="font-semibold text-white glow-text text-sm">
                      {testimonial.name}
                    </div>
                    <div className="text-xs text-gray-400">
                      {testimonial.role}
                    </div>
                    <div className="text-xs text-blue-400">
                      {testimonial.company}
                    </div>
                  </div>
                </div>
              </motion.div>
            );
          })}
        </div>

        {/* Bottom CTA */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.8 }}
          className="text-center mt-20"
        >
          <div className="muted-glassmorphic rounded-3xl p-10 relative overflow-hidden">
            {/* Muted cosmic background for CTA */}
            <div className="absolute inset-0 dark-blur-gradient opacity-30 rounded-3xl"></div>
            <div className="relative z-10">
              <h3 className="text-3xl font-bold text-white mb-6 glow-text font-orbitron">
                Ready to Write Your Cosmic Success Story?
              </h3>
              <p className="text-gray-300 mb-8 max-w-2xl mx-auto text-lg leading-relaxed">
                Join thousands of space professionals who have advanced their careers in cosmic technology.
                Your galactic journey to success starts with a single step into the stars.
              </p>
              <button className="bg-gradient-to-r from-blue-600 to-pink-600 text-white px-10 py-4 rounded-lg font-semibold hover:from-pink-600 hover:to-purple-600 transition-all duration-300">
                Start Your Journey Today
              </button>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
