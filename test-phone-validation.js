// Test script for phone validation
// Run with: node test-phone-validation.js

// Mock the PhoneValidator class for testing
class PhoneValidator {
  static readonly COUNTRY_CODE = '+254';
  
  static readonly VALID_PREFIXES = [
    '70', '71', '72', '73', '74', '75', '76', '77', '78', '79', // Safaricom
    '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', // Safaricom
    '20', '21', '22', '23', '24', '25', '26', '27', '28', '29', // Airtel
    '30', '31', '32', '33', '34', '35', '36', '37', '38', '39', // Airtel
    '40', '41', '42', '43', '44', '45', '46', '47', '48', '49', // Airtel
    '50', '51', '52', '53', '54', '55', '56', '57', '58', '59', // Telkom
    '60', '61', '62', '63', '64', '65', '66', '67', '68', '69', // Telkom
  ];

  static validateAndFormat(phoneNumber) {
    if (!phoneNumber || typeof phoneNumber !== 'string') {
      return {
        isValid: false,
        formatted: '',
        error: 'Phone number is required'
      };
    }

    const cleaned = phoneNumber.replace(/[^\d+]/g, '');
    let digits = '';
    
    if (cleaned.startsWith('+254')) {
      digits = cleaned.substring(4);
    } else if (cleaned.startsWith('254')) {
      digits = cleaned.substring(3);
    } else if (cleaned.startsWith('0')) {
      digits = cleaned.substring(1);
    } else if (cleaned.length === 9) {
      digits = cleaned;
    } else {
      return {
        isValid: false,
        formatted: '',
        error: 'Invalid phone number format. Please use Kenyan format: +254 XXXXXXXXX'
      };
    }

    if (digits.length !== 9) {
      return {
        isValid: false,
        formatted: '',
        error: 'Phone number must have exactly 9 digits after the country code'
      };
    }

    if (!/^\d{9}$/.test(digits)) {
      return {
        isValid: false,
        formatted: '',
        error: 'Phone number must contain only digits'
      };
    }

    const prefix = digits.substring(0, 2);
    if (!this.VALID_PREFIXES.includes(prefix)) {
      return {
        isValid: false,
        formatted: '',
        error: 'Invalid Kenyan mobile network prefix'
      };
    }

    const formatted = `${this.COUNTRY_CODE} ${digits}`;
    return {
      isValid: true,
      formatted,
    };
  }

  static formatForStorage(phoneNumber) {
    const result = this.validateAndFormat(phoneNumber);
    return result.isValid ? result.formatted.replace(/\s/g, '') : null;
  }

  static formatForDisplay(phoneNumber) {
    const result = this.validateAndFormat(phoneNumber);
    return result.isValid ? result.formatted : phoneNumber;
  }
}

// Test cases
const testCases = [
  // Valid formats
  '+254 700 123 456',
  '+254700123456',
  '254700123456',
  '0700123456',
  '700123456',
  
  // Different network prefixes
  '+254 701 123 456', // Safaricom
  '+254 722 123 456', // Safaricom
  '+254 733 123 456', // Airtel
  '+254 750 123 456', // Telkom
  
  // Invalid formats
  '+255 700 123 456', // Wrong country code
  '+254 800 123 456', // Invalid prefix
  '+254 70 123 456',  // Too short
  '+254 7001 123 456', // Too long
  'invalid',
  '',
  null,
];

console.log('=== Phone Number Validation Tests ===\n');

testCases.forEach((testCase, index) => {
  console.log(`Test ${index + 1}: "${testCase}"`);
  const result = PhoneValidator.validateAndFormat(testCase);
  
  if (result.isValid) {
    console.log(`  ✅ Valid: ${result.formatted}`);
    console.log(`  📱 Storage: ${PhoneValidator.formatForStorage(testCase)}`);
    console.log(`  👁️  Display: ${PhoneValidator.formatForDisplay(testCase)}`);
  } else {
    console.log(`  ❌ Invalid: ${result.error}`);
  }
  console.log('');
});

console.log('=== Test Summary ===');
const validCount = testCases.filter(tc => PhoneValidator.validateAndFormat(tc).isValid).length;
const totalCount = testCases.filter(tc => tc !== null).length;
console.log(`Valid: ${validCount}/${totalCount} test cases`);
console.log('Expected: 10/15 test cases should be valid');
