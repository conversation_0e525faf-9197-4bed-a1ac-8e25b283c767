import mongoose, { Document, Schema } from 'mongoose';

export interface IMentorshipSession extends Document {
  _id: string;
  mentorId: string;
  menteeId: string;
  title: string;
  description?: string;
  scheduledAt: Date;
  duration: number; // in minutes
  status: 'scheduled' | 'in_progress' | 'completed' | 'cancelled' | 'no_show';
  meetingLink?: string;
  meetingType: 'video' | 'voice' | 'in_person' | 'chat';
  notes?: {
    mentorNotes?: string;
    menteeNotes?: string;
    actionItems?: string[];
  };
  feedback?: {
    mentorRating?: number;
    menteeRating?: number;
    mentorReview?: string;
    menteeReview?: string;
  };
  price?: number;
  currency?: string;
  isPaid: boolean;
  paymentStatus?: 'pending' | 'completed' | 'failed' | 'refunded';
  remindersSent: {
    mentor: boolean;
    mentee: boolean;
  };
  createdAt: Date;
  updatedAt: Date;
}

export interface IMentorshipProgram extends Document {
  _id: string;
  mentorId: string;
  title: string;
  description: string;
  category: string;
  duration: number; // in weeks
  maxMentees: number;
  currentMentees: number;
  price?: number;
  currency?: string;
  isFree: boolean;
  skills: string[];
  learningOutcomes: string[];
  requirements: string[];
  schedule: {
    sessionsPerWeek: number;
    sessionDuration: number; // in minutes
    preferredDays: string[];
    preferredTimes: string[];
  };
  isActive: boolean;
  enrolledMentees: string[];
  createdAt: Date;
  updatedAt: Date;
}

export interface IMentorProfile extends Document {
  _id: string;
  userId: string;
  isApproved: boolean;
  expertise: string[];
  specialization: string;
  experience: number; // years
  education: string[];
  certifications: string[];
  languages: string[];
  timezone: string;
  availability: {
    days: string[];
    timeSlots: {
      start: string;
      end: string;
    }[];
  };
  pricing: {
    hourlyRate?: number;
    currency?: string;
    offersFreeSession: boolean;
  };
  bio: string;
  achievements: string[];
  totalSessions: number;
  averageRating: number;
  totalRatings: number;
  responseTime: number; // in hours
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

const MentorshipSessionSchema = new Schema<IMentorshipSession>({
  mentorId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  menteeId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  title: {
    type: String,
    required: true,
    trim: true,
  },
  description: String,
  scheduledAt: {
    type: Date,
    required: true,
  },
  duration: {
    type: Number,
    required: true,
    default: 60,
  },
  status: {
    type: String,
    enum: ['scheduled', 'in_progress', 'completed', 'cancelled', 'no_show'],
    default: 'scheduled',
  },
  meetingLink: String,
  meetingType: {
    type: String,
    enum: ['video', 'voice', 'in_person', 'chat'],
    default: 'video',
  },
  notes: {
    mentorNotes: String,
    menteeNotes: String,
    actionItems: [String],
  },
  feedback: {
    mentorRating: {
      type: Number,
      min: 1,
      max: 5,
    },
    menteeRating: {
      type: Number,
      min: 1,
      max: 5,
    },
    mentorReview: String,
    menteeReview: String,
  },
  price: Number,
  currency: {
    type: String,
    default: 'USD',
  },
  isPaid: {
    type: Boolean,
    default: false,
  },
  paymentStatus: {
    type: String,
    enum: ['pending', 'completed', 'failed', 'refunded'],
  },
  remindersSent: {
    mentor: { type: Boolean, default: false },
    mentee: { type: Boolean, default: false },
  },
}, {
  timestamps: true,
});

const MentorshipProgramSchema = new Schema<IMentorshipProgram>({
  mentorId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  title: {
    type: String,
    required: true,
    trim: true,
  },
  description: {
    type: String,
    required: true,
  },
  category: {
    type: String,
    required: true,
  },
  duration: {
    type: Number,
    required: true,
  },
  maxMentees: {
    type: Number,
    required: true,
    default: 5,
  },
  currentMentees: {
    type: Number,
    default: 0,
  },
  price: Number,
  currency: {
    type: String,
    default: 'USD',
  },
  isFree: {
    type: Boolean,
    default: true,
  },
  skills: [String],
  learningOutcomes: [String],
  requirements: [String],
  schedule: {
    sessionsPerWeek: { type: Number, default: 1 },
    sessionDuration: { type: Number, default: 60 },
    preferredDays: [String],
    preferredTimes: [String],
  },
  isActive: {
    type: Boolean,
    default: true,
  },
  enrolledMentees: [{
    type: Schema.Types.ObjectId,
    ref: 'User',
  }],
}, {
  timestamps: true,
});

const MentorProfileSchema = new Schema<IMentorProfile>({
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    unique: true,
  },
  isApproved: {
    type: Boolean,
    default: false,
  },
  expertise: [String],
  specialization: String,
  experience: Number,
  education: [String],
  certifications: [String],
  languages: [String],
  timezone: String,
  availability: {
    days: [String],
    timeSlots: [{
      start: String,
      end: String,
    }],
  },
  pricing: {
    hourlyRate: Number,
    currency: { type: String, default: 'USD' },
    offersFreeSession: { type: Boolean, default: false },
  },
  bio: String,
  achievements: [String],
  totalSessions: { type: Number, default: 0 },
  averageRating: { type: Number, default: 0 },
  totalRatings: { type: Number, default: 0 },
  responseTime: { type: Number, default: 24 },
  isActive: { type: Boolean, default: true },
}, {
  timestamps: true,
});

// Indexes
MentorshipSessionSchema.index({ mentorId: 1, scheduledAt: 1 });
MentorshipSessionSchema.index({ menteeId: 1, scheduledAt: 1 });
MentorshipSessionSchema.index({ status: 1 });
MentorshipSessionSchema.index({ scheduledAt: 1 });

MentorshipProgramSchema.index({ mentorId: 1 });
MentorshipProgramSchema.index({ category: 1 });
MentorshipProgramSchema.index({ isActive: 1 });

// userId already has unique index
MentorProfileSchema.index({ expertise: 1 });
MentorProfileSchema.index({ isApproved: 1, isActive: 1 });

export const MentorshipSession = mongoose.models.MentorshipSession || mongoose.model<IMentorshipSession>('MentorshipSession', MentorshipSessionSchema);
export const MentorshipProgram = mongoose.models.MentorshipProgram || mongoose.model<IMentorshipProgram>('MentorshipProgram', MentorshipProgramSchema);
export const MentorProfile = mongoose.models.MentorProfile || mongoose.model<IMentorProfile>('MentorProfile', MentorProfileSchema);
