'use client';

import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';

export default function DashboardRedirect() {
  const { data: session, status } = useSession();
  const router = useRouter();

  useEffect(() => {
    if (status === 'loading') return; // Still loading

    if (!session) {
      router.push('/auth/signin');
      return;
    }

    // Redirect to role-specific dashboard
    const roleDashboards = {
      admin: '/dashboard/admin',
      student: '/dashboard/student',
      mentor: '/dashboard/mentor',
      organisation: '/dashboard/organisation',
    };

    const userRole = (session.user as any)?.role;
    const dashboardUrl = roleDashboards[userRole as keyof typeof roleDashboards];

    if (dashboardUrl) {
      router.push(dashboardUrl);
    } else {
      // Default to student dashboard if role is unknown
      router.push('/dashboard/student');
    }
  }, [session, status, router]);

  // Show loading state while redirecting
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p className="text-gray-600">Redirecting to your dashboard...</p>
      </div>
    </div>
  );
}
