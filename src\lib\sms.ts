import AfricasTalking from 'africastalking';

const africastalking = AfricasTalking({
  apiKey: process.env.AFRICASTALKING_API_KEY!,
  username: process.env.AFRICASTALKING_USERNAME!,
});

const sms = africastalking.SMS;

export interface SMSOptions {
  to: string | string[];
  message: string;
  from?: string;
}

export class SMSService {
  // Default sender ID for Nova platform
  private static readonly DEFAULT_SENDER_ID = 'AFTKNG';

  static async sendSMS({ to, message, from }: SMSOptions) {
    try {
      const options = {
        to: Array.isArray(to) ? to : [to],
        message,
        from: from || this.DEFAULT_SENDER_ID,
      };

      const result = await sms.send(options);
      console.log('SMS sent successfully:', result);
      return result;
    } catch (error) {
      console.error('Error sending SMS:', error);
      throw error;
    }
  }

  // Predefined SMS templates for Nova platform
  static async sendWelcomeSMS(phoneNumber: string, name: string) {
    const message = `Welcome to Nova, ${name}! 🚀 Your journey into space technology and innovation starts here. Access your dashboard at nova.africa`;
    return this.sendSMS({ to: phoneNumber, message });
  }

  static async sendCourseReminder(phoneNumber: string, courseName: string) {
    const message = `📚 Don't forget to continue your "${courseName}" course on Nova. Keep building your space tech skills! Login at nova.africa`;
    return this.sendSMS({ to: phoneNumber, message });
  }

  static async sendMentorshipBooking(phoneNumber: string, mentorName: string, sessionTime: string) {
    const message = `✅ Your mentorship session with ${mentorName} is confirmed for ${sessionTime}. Check your Nova dashboard for meeting details.`;
    return this.sendSMS({ to: phoneNumber, message });
  }

  static async sendJobAlert(phoneNumber: string, jobTitle: string, company: string) {
    const message = `🎯 New job opportunity: ${jobTitle} at ${company}. This matches your profile! Apply now on Nova: nova.africa/jobs`;
    return this.sendSMS({ to: phoneNumber, message });
  }

  static async sendCertificationAlert(phoneNumber: string, courseName: string) {
    const message = `🎓 Congratulations! You've earned a certificate for "${courseName}". Download it from your Nova dashboard: nova.africa/certificates`;
    return this.sendSMS({ to: phoneNumber, message });
  }

  static async sendEventReminder(phoneNumber: string, eventName: string, eventTime: string) {
    const message = `📅 Reminder: "${eventName}" starts at ${eventTime}. Don't miss this opportunity! Join via Nova: nova.africa/events`;
    return this.sendSMS({ to: phoneNumber, message });
  }

  // Additional SMS templates for enhanced notification system
  static async sendJobApplicationSubmitted(phoneNumber: string, applicantName: string, jobTitle: string, companyName: string) {
    const message = `✅ Application submitted! Hi ${applicantName}, your application for "${jobTitle}" at ${companyName} has been received. Track your application on Nova: nova.africa/dashboard`;
    return this.sendSMS({ to: phoneNumber, message });
  }

  static async sendNewJobApplication(phoneNumber: string, employerName: string, applicantName: string, jobTitle: string) {
    const message = `📋 New application received! ${applicantName} has applied for "${jobTitle}". Review the application on your Nova dashboard: nova.africa/dashboard/organisation`;
    return this.sendSMS({ to: phoneNumber, message });
  }

  static async sendApplicationStatusUpdate(phoneNumber: string, applicantName: string, jobTitle: string, status: string) {
    const statusMessages = {
      'under_review': 'is now under review',
      'shortlisted': 'has been shortlisted',
      'interview_scheduled': 'has an interview scheduled',
      'rejected': 'was not selected this time',
      'hired': 'has been accepted! Congratulations!'
    };
    const statusText = statusMessages[status as keyof typeof statusMessages] || 'has been updated';
    const message = `📢 Application update: Your application for "${jobTitle}" ${statusText}. Check your Nova dashboard for details: nova.africa/dashboard`;
    return this.sendSMS({ to: phoneNumber, message });
  }

  static async sendSessionReminder(phoneNumber: string, sessionTitle: string, sessionTime: string, mentorName: string) {
    const message = `⏰ Session reminder: "${sessionTitle}" with ${mentorName} starts at ${sessionTime}. Join via your Nova dashboard: nova.africa/dashboard`;
    return this.sendSMS({ to: phoneNumber, message });
  }

  static async sendEventRegistrationConfirmation(phoneNumber: string, userName: string, eventTitle: string, eventDate: string) {
    const message = `🎉 Registration confirmed! Hi ${userName}, you're registered for "${eventTitle}" on ${eventDate}. Event details: nova.africa/events`;
    return this.sendSMS({ to: phoneNumber, message });
  }

  static async sendCourseEnrollmentConfirmation(phoneNumber: string, userName: string, courseName: string) {
    const message = `📚 Enrollment confirmed! Hi ${userName}, you're now enrolled in "${courseName}". Start learning: nova.africa/courses`;
    return this.sendSMS({ to: phoneNumber, message });
  }

  static async sendSystemNotification(phoneNumber: string, title: string, message: string) {
    const smsMessage = `🔔 ${title}: ${message} - Nova Platform`;
    return this.sendSMS({ to: phoneNumber, message: smsMessage });
  }
}
