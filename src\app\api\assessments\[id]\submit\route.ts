import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { connectDB } from '@/lib/mongodb';
import { SkillAssessment } from '@/models/Certificate';
import { GeminiService } from '@/lib/gemini';

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    await connectDB();

    const assessment = await SkillAssessment.findById(params.id);
    if (!assessment) {
      return NextResponse.json(
        { success: false, error: 'Assessment not found' },
        { status: 404 }
      );
    }

    // Check if user owns this assessment
    if (assessment.userId.toString() !== session.user.id) {
      return NextResponse.json(
        { success: false, error: 'Access denied' },
        { status: 403 }
      );
    }

    // Check if assessment is still active
    if (assessment.status !== 'in_progress') {
      return NextResponse.json(
        { success: false, error: 'Assessment is not active' },
        { status: 400 }
      );
    }

    // Check if assessment has expired
    if (new Date() > assessment.expiresAt) {
      assessment.status = 'expired';
      await assessment.save();
      return NextResponse.json(
        { success: false, error: 'Assessment has expired' },
        { status: 400 }
      );
    }

    const body = await request.json();
    const { answers, timeSpent } = body;

    if (!answers || !Array.isArray(answers)) {
      return NextResponse.json(
        { success: false, error: 'Invalid answers format' },
        { status: 400 }
      );
    }

    // Update questions with user answers
    let correctAnswers = 0;
    let totalScore = 0;
    let maxPossibleScore = 0;

    assessment.questions.forEach((question, index) => {
      if (answers[index] !== undefined) {
        question.userAnswer = answers[index];
        
        maxPossibleScore += question.points;
        
        // Check if answer is correct
        if (question.type === 'multiple_choice' || question.type === 'true_false') {
          if (question.userAnswer === question.correctAnswer) {
            correctAnswers++;
            totalScore += question.points;
          }
        } else if (question.type === 'short_answer') {
          // Simple string comparison for short answers
          const userAnswer = String(question.userAnswer).toLowerCase().trim();
          const correctAnswer = String(question.correctAnswer).toLowerCase().trim();
          if (userAnswer === correctAnswer) {
            correctAnswers++;
            totalScore += question.points;
          }
        }
        // Essay and code questions will be evaluated by AI
      }
    });

    const percentage = Math.round((totalScore / maxPossibleScore) * 100);

    // Determine skill level based on percentage
    let skillLevel: 'beginner' | 'intermediate' | 'advanced' | 'expert' = 'beginner';
    if (percentage >= 90) skillLevel = 'expert';
    else if (percentage >= 75) skillLevel = 'advanced';
    else if (percentage >= 60) skillLevel = 'intermediate';

    // Generate strengths and weaknesses based on performance
    const strengths: string[] = [];
    const weaknesses: string[] = [];
    const recommendations: string[] = [];

    if (percentage >= 80) {
      strengths.push('Strong understanding of core concepts');
      strengths.push('Excellent problem-solving skills');
    } else if (percentage >= 60) {
      strengths.push('Good grasp of fundamental principles');
      weaknesses.push('Some areas need reinforcement');
      recommendations.push('Review advanced topics and practice more complex problems');
    } else {
      weaknesses.push('Basic concepts need strengthening');
      weaknesses.push('More practice required');
      recommendations.push('Start with foundational courses');
      recommendations.push('Seek mentorship for guided learning');
    }

    // Update assessment results
    assessment.results = {
      totalQuestions: assessment.questions.length,
      correctAnswers,
      score: totalScore,
      percentage,
      timeSpent: timeSpent || 0,
      skillLevel,
      strengths,
      weaknesses,
      recommendations,
    };

    assessment.status = 'completed';
    assessment.completedAt = new Date();

    // Generate AI analysis for essay/code questions
    try {
      const essayQuestions = assessment.questions.filter(q => 
        q.type === 'essay' || q.type === 'code'
      );

      if (essayQuestions.length > 0) {
        const aiAnalysis = await GeminiService.generateSkillAssessment(
          essayQuestions.map(q => ({
            question: q.question,
            userAnswer: q.userAnswer,
            correctAnswer: q.correctAnswer,
          })),
          assessment.skillArea
        );

        assessment.aiAnalysis = {
          detailedFeedback: aiAnalysis,
          learningPath: recommendations,
          nextSteps: [
            'Complete relevant courses on Nova platform',
            'Join study groups in the community',
            'Find a mentor in your area of interest',
          ],
          estimatedImprovementTime: Math.max(4, Math.ceil((100 - percentage) / 10)),
        };
      }
    } catch (aiError) {
      console.error('Error generating AI analysis:', aiError);
      // Continue without AI analysis if it fails
    }

    await assessment.save();

    return NextResponse.json({
      success: true,
      data: {
        results: assessment.results,
        aiAnalysis: assessment.aiAnalysis,
        skillLevel,
        canGetCertificate: percentage >= 70, // Minimum 70% for certification
      },
      message: 'Assessment completed successfully',
    });
  } catch (error) {
    console.error('Error submitting assessment:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to submit assessment' },
      { status: 500 }
    );
  }
}
