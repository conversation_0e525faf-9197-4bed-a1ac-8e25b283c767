import { NextRequest, NextResponse } from 'next/server';
import { requireAuth } from '@/lib/auth-middleware';
import { NotificationService, NotificationOptions } from '@/lib/notification-service';
import { User } from '@/models/User';
import { connectDB } from '@/lib/mongodb';

export async function POST(request: NextRequest) {
  return requireAuth(request, async (req) => {
    try {
      await connectDB();

      const body = await req.json();
      const {
        userId,
        phone,
        email,
        title,
        message,
        type = 'custom',
        priority = 'medium',
        data
      } = body;

      // Validate required fields
      if (!title || !message) {
        return NextResponse.json(
          { success: false, error: 'Title and message are required' },
          { status: 400 }
        );
      }

      let user = null;

      // Get user if userId is provided
      if (userId) {
        user = await User.findById(userId);
        if (!user) {
          return NextResponse.json(
            { success: false, error: 'User not found' },
            { status: 404 }
          );
        }
      }

      // Check permissions - only admins and organisations can send notifications
      if (!['admin', 'organisation'].includes(req.user?.role)) {
        return NextResponse.json(
          { success: false, error: 'Insufficient permissions to send notifications' },
          { status: 403 }
        );
      }

      const notificationOptions: NotificationOptions = {
        userId,
        phone,
        email,
        title,
        message,
        type,
        priority,
        data
      };

      const result = await NotificationService.sendNotification(notificationOptions, user);

      return NextResponse.json({
        success: true,
        data: result,
        message: 'Notification sent successfully'
      });

    } catch (error) {
      console.error('Error sending notification:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to send notification' },
        { status: 500 }
      );
    }
  });
}

// Bulk notification endpoint
export async function PUT(request: NextRequest) {
  return requireAuth(request, async (req) => {
    try {
      await connectDB();

      const body = await req.json();
      const {
        userIds,
        role,
        title,
        message,
        type = 'custom',
        priority = 'medium',
        data
      } = body;

      // Validate required fields
      if (!title || !message) {
        return NextResponse.json(
          { success: false, error: 'Title and message are required' },
          { status: 400 }
        );
      }

      // Check permissions - only admins can send bulk notifications
      if (req.user?.role !== 'admin') {
        return NextResponse.json(
          { success: false, error: 'Only administrators can send bulk notifications' },
          { status: 403 }
        );
      }

      let users = [];

      if (userIds && Array.isArray(userIds)) {
        // Send to specific users
        users = await User.find({ _id: { $in: userIds }, isActive: true });
      } else if (role) {
        // Send to all users with specific role
        users = await User.find({ role, isActive: true });
      } else {
        return NextResponse.json(
          { success: false, error: 'Either userIds or role must be specified' },
          { status: 400 }
        );
      }

      if (users.length === 0) {
        return NextResponse.json(
          { success: false, error: 'No users found matching the criteria' },
          { status: 404 }
        );
      }

      const notificationOptions = {
        title,
        message,
        type,
        priority,
        data
      };

      const results = await NotificationService.sendBulkNotification(users, notificationOptions);

      const successCount = results.filter(r => r.success).length;
      const failureCount = results.length - successCount;

      return NextResponse.json({
        success: true,
        data: {
          totalSent: results.length,
          successCount,
          failureCount,
          results
        },
        message: `Bulk notification sent to ${successCount} users (${failureCount} failed)`
      });

    } catch (error) {
      console.error('Error sending bulk notification:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to send bulk notification' },
        { status: 500 }
      );
    }
  });
}
