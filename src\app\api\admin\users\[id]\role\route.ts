import { NextRequest, NextResponse } from 'next/server';
import { connectDB } from '@/lib/mongodb';
import { User } from '@/models/User';
import { requireAdmin } from '@/lib/auth-middleware';
import { UserRole, PermissionChecker } from '@/lib/permissions';
import { SMSService } from '@/lib/sms';

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  return requireAdmin(request, async (req) => {
    try {
      await connectDB();

      const body = await request.json();
      const { role, reason } = body;

      // Validate role
      const validRoles: UserRole[] = ['admin', 'student', 'mentor', 'employer', 'partner'];
      if (!validRoles.includes(role)) {
        return NextResponse.json(
          { success: false, error: 'Invalid role specified' },
          { status: 400 }
        );
      }

      // Get target user
      const targetUser = await User.findById(params.id);
      if (!targetUser) {
        return NextResponse.json(
          { success: false, error: 'User not found' },
          { status: 404 }
        );
      }

      // Prevent self-role modification for admins (security measure)
      if (targetUser._id.toString() === req.user?.id && role !== 'admin') {
        return NextResponse.json(
          { success: false, error: 'Cannot modify your own admin role' },
          { status: 400 }
        );
      }

      const previousRole = targetUser.role;

      // Update user role
      targetUser.role = role;
      targetUser.roleHistory = targetUser.roleHistory || [];
      targetUser.roleHistory.push({
        previousRole,
        newRole: role,
        changedBy: req.user?.id,
        changedAt: new Date(),
        reason: reason || 'Role updated by admin',
      });

      // Role-specific updates
      switch (role) {
        case 'mentor':
          targetUser.mentorship = {
            isMentor: true,
            isApproved: false, // Requires approval
            appliedAt: new Date(),
          };
          break;

        case 'employer':
          targetUser.employment = {
            isEmployer: true,
            companyVerified: false, // Requires verification
            appliedAt: new Date(),
          };
          break;

        case 'partner':
          targetUser.partnership = {
            isPartner: true,
            isApproved: false, // Requires approval
            appliedAt: new Date(),
          };
          break;

        case 'admin':
          // Log admin creation for security
          console.log(`New admin created: ${targetUser.email} by ${req.user?.email}`);
          break;
      }

      await targetUser.save();

      // Send notification to user about role change
      try {
        if (targetUser.phone) {
          await SMSService.sendSMS(
            targetUser.phone,
            'role_updated',
            {
              userName: targetUser.name,
              newRole: role,
              previousRole,
              reason: reason || 'Role updated by administrator',
            }
          );
        }
      } catch (smsError) {
        console.error('Error sending role update SMS:', smsError);
      }

      // Return updated user without sensitive data
      const { password, ...userWithoutPassword } = targetUser.toObject();

      return NextResponse.json({
        success: true,
        data: userWithoutPassword,
        message: `User role updated from ${previousRole} to ${role}`,
      });
    } catch (error) {
      console.error('Error updating user role:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to update user role' },
        { status: 500 }
      );
    }
  });
}

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  return requireAdmin(request, async (req) => {
    try {
      await connectDB();

      const user = await User.findById(params.id).select('-password');
      if (!user) {
        return NextResponse.json(
          { success: false, error: 'User not found' },
          { status: 404 }
        );
      }

      // Get user's current permissions
      const permissions = PermissionChecker.getPermissionsForRole(user.role as UserRole);

      return NextResponse.json({
        success: true,
        data: {
          user,
          permissions,
          roleHistory: user.roleHistory || [],
        },
      });
    } catch (error) {
      console.error('Error fetching user role info:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to fetch user role information' },
        { status: 500 }
      );
    }
  });
}
