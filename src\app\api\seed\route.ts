import { NextRequest, NextResponse } from 'next/server';
import { seedDatabase } from '@/lib/seed-data';
import { type SeedConfig } from '@/lib/seed-config';

export async function POST(request: NextRequest) {
  try {
    // Only allow seeding in development
    if (process.env.NODE_ENV !== 'development') {
      return NextResponse.json(
        { success: false, error: 'Seeding only allowed in development' },
        { status: 403 }
      );
    }

    // Parse request body for custom configuration
    let customConfig: Partial<SeedConfig> | undefined;
    try {
      const body = await request.json();
      customConfig = body.config;
    } catch {
      // No body or invalid JSON, use default config
    }

    await seedDatabase(customConfig);

    return NextResponse.json({
      success: true,
      message: 'Database seeded successfully',
      config: customConfig || 'Using environment configuration',
    });
  } catch (error) {
    console.error('Error seeding database:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to seed database' },
      { status: 500 }
    );
  }
}
