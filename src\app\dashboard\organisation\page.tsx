'use client';

import React, { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import Link from 'next/link';
import { RoleGuard } from '@/components/auth/RoleGuard';

interface OrganisationStats {
  activeJobs: number;
  totalApplications: number;
  newApplications: number;
  interviewsScheduled: number;
  hiredCandidates: number;
  profileViews: number;
  activeCourses: number;
  totalStudents: number;
  upcomingEvents: number;
  communityPosts: number;
  certificatesIssued: number;
  mentorshipSessions: number;
}

interface JobPosting {
  _id: string;
  title: string;
  location: string;
  type: string;
  applicationsCount: number;
  status: string;
  createdAt: string;
  deadline?: string;
}

interface RecentApplication {
  _id: string;
  jobId: {
    _id: string;
    title: string;
  };
  applicantId: {
    name: string;
    email: string;
    avatar?: string;
  };
  status: string;
  appliedAt: string;
  coverLetter?: string;
}

export default function OrganisationDashboard() {
  const { data: session } = useSession();
  const userRole = (session?.user as any)?.role;

  const [stats, setStats] = useState<OrganisationStats>({
    activeJobs: 0,
    totalApplications: 0,
    newApplications: 0,
    interviewsScheduled: 0,
    hiredCandidates: 0,
    profileViews: 0,
    activeCourses: 0,
    totalStudents: 0,
    upcomingEvents: 0,
    communityPosts: 0,
    certificatesIssued: 0,
    mentorshipSessions: 0,
  });
  const [jobPostings, setJobPostings] = useState<JobPosting[]>([]);
  const [recentApplications, setRecentApplications] = useState<RecentApplication[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      const [statsRes, jobsRes, applicationsRes] = await Promise.all([
        fetch('/api/dashboard/organisation/stats'),
        fetch('/api/dashboard/organisation/jobs?limit=5'),
        fetch('/api/dashboard/organisation/applications?limit=5'),
      ]);

      if (statsRes.ok) {
        const statsData = await statsRes.json();
        setStats(statsData.data);
      }

      if (jobsRes.ok) {
        const jobsData = await jobsRes.json();
        setJobPostings(jobsData.data);
      }

      if (applicationsRes.ok) {
        const applicationsData = await applicationsRes.json();
        setRecentApplications(applicationsData.data);
      }
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <RoleGuard allowedRoles={['organisation']}>
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">
            Organization Dashboard
          </h1>
          <p className="text-gray-600 mt-2">
            Comprehensive platform for job postings, recruitment, regional programs, events, community engagement, and educational initiatives.
          </p>
        </div>

          {/* Stats Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {/* Recruitment Stats */}
            <StatCard
              title="Active Jobs"
              value={stats.activeJobs}
              icon="💼"
              link="/dashboard/organisation/jobs"
            />
            <StatCard
              title="New Applications"
              value={stats.newApplications}
              icon="🆕"
              link="/dashboard/organisation/applications?filter=new"
              highlight={stats.newApplications > 0}
            />
            <StatCard
              title="Interviews Scheduled"
              value={stats.interviewsScheduled}
              icon="📅"
              link="/dashboard/organisation/interviews"
            />
            <StatCard
              title="Hired Candidates"
              value={stats.hiredCandidates}
              icon="✅"
              link="/dashboard/organisation/hired"
            />

            {/* Education & Training Stats */}
            <StatCard
              title="Active Courses"
              value={stats.activeCourses}
              icon="📚"
              link="/dashboard/organisation/courses"
            />
            <StatCard
              title="Total Students"
              value={stats.totalStudents}
              icon="👥"
              link="/dashboard/organisation/students"
            />
            <StatCard
              title="Certificates Issued"
              value={stats.certificatesIssued}
              icon="🎓"
              link="/dashboard/organisation/certificates"
            />
            <StatCard
              title="Mentorship Sessions"
              value={stats.mentorshipSessions}
              icon="🤝"
              link="/dashboard/organisation/mentorship"
            />

            {/* Community & Events Stats */}
            <StatCard
              title="Upcoming Events"
              value={stats.upcomingEvents}
              icon="🎯"
              link="/dashboard/organisation/events"
            />
            <StatCard
              title="Community Posts"
              value={stats.communityPosts}
              icon="💬"
              link="/dashboard/organisation/community"
            />
            <StatCard
              title="Profile Views"
              value={stats.profileViews}
              icon="👁️"
              link="/dashboard/organisation/analytics"
            />
            <StatCard
              title="Total Applications"
              value={stats.totalApplications}
              icon="📄"
              link="/dashboard/organisation/applications"
            />
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Job Postings */}
            <div className="lg:col-span-2">
              <div className="bg-white rounded-lg shadow p-6">
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-xl font-semibold text-gray-900">Recent Job Postings</h2>
                  <Link
                    href="/jobs/create"
                    className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm"
                  >
                    Post New Job
                  </Link>
                </div>

                {jobPostings.length === 0 ? (
                  <div className="text-center py-8">
                    <p className="text-gray-500 mb-4">You haven't posted any jobs yet.</p>
                    <Link
                      href="/jobs/create"
                      className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                    >
                      Post Your First Job
                    </Link>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {jobPostings.map((job) => (
                      <JobCard key={job._id} job={job} />
                    ))}
                  </div>
                )}
              </div>
            </div>

            {/* Quick Actions & Recent Applications */}
            <div className="space-y-6">
              {/* Quick Actions */}
              <div className="bg-white rounded-lg shadow p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">Quick Actions</h2>
                <div className="space-y-3">
                  <QuickActionButton
                    href="/jobs/create"
                    icon="💼"
                    title="Post New Job"
                    description="Find the right candidates"
                  />
                  <QuickActionButton
                    href="/dashboard/organisation/applications"
                    icon="📄"
                    title="Review Applications"
                    description="Manage candidate applications"
                  />
                  <QuickActionButton
                    href="/events/create"
                    icon="🎯"
                    title="Create Event"
                    description="Host job fairs, workshops, or regional programs"
                  />
                  <QuickActionButton
                    href="/courses/create"
                    icon="📚"
                    title="Create Course"
                    description="Develop educational content and training"
                  />
                  <QuickActionButton
                    href="/community/moderate"
                    icon="🛡️"
                    title="Moderate Community"
                    description="Manage community discussions and content"
                  />
                  <QuickActionButton
                    href="/mentorship/create"
                    icon="🤝"
                    title="Offer Mentorship"
                    description="Provide guidance and expertise"
                  />
                  <QuickActionButton
                    href="/resources/upload"
                    icon="📁"
                    title="Upload Resources"
                    description="Share educational materials and tools"
                  />
                  <QuickActionButton
                    href="/certificates/create"
                    icon="🎓"
                    title="Issue Certificates"
                    description="Generate certificates for programs"
                  />
                  <QuickActionButton
                    href="/dashboard/organisation/analytics"
                    icon="📊"
                    title="View Analytics"
                    description="Track performance and engagement metrics"
                  />
                </div>
              </div>

              {/* Recent Applications */}
              <div className="bg-white rounded-lg shadow p-6">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-xl font-semibold text-gray-900">Recent Applications</h2>
                  <Link
                    href="/dashboard/organisation/applications"
                    className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                  >
                    View All
                  </Link>
                </div>
                {recentApplications.length === 0 ? (
                  <p className="text-gray-500 text-sm">No applications yet</p>
                ) : (
                  <div className="space-y-3">
                    {recentApplications.map((application) => (
                      <ApplicationCard key={application._id} application={application} />
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
    </RoleGuard>
  );
}

function StatCard({
  title,
  value,
  icon,
  link,
  highlight = false
}: {
  title: string;
  value: number;
  icon: string;
  link?: string;
  highlight?: boolean;
}) {
  const content = (
    <div className={`bg-white rounded-lg shadow p-6 hover:shadow-md transition-shadow ${
      highlight ? 'ring-2 ring-blue-500' : ''
    }`}>
      <div className="flex items-center">
        <div className="text-2xl mr-3">{icon}</div>
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-2xl font-bold text-gray-900">{value}</p>
        </div>
      </div>
      {highlight && (
        <div className="mt-2">
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            New
          </span>
        </div>
      )}
    </div>
  );

  return link ? <Link href={link}>{content}</Link> : content;
}

function JobCard({ job }: { job: JobPosting }) {
  const isExpiringSoon = job.deadline &&
    new Date(job.deadline).getTime() - new Date().getTime() < 7 * 24 * 60 * 60 * 1000; // 7 days

  return (
    <div className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <h3 className="font-medium text-gray-900">{job.title}</h3>
          <div className="flex items-center space-x-4 mt-1 text-sm text-gray-600">
            <span>📍 {job.location}</span>
            <span>💼 {job.type}</span>
            <span>📄 {job.applicationsCount} applications</span>
          </div>
          <div className="flex items-center space-x-2 mt-2">
            <span className={`px-2 py-1 text-xs rounded-full ${
              job.status === 'active'
                ? 'bg-green-100 text-green-800'
                : 'bg-gray-100 text-gray-800'
            }`}>
              {job.status}
            </span>
            {isExpiringSoon && (
              <span className="px-2 py-1 text-xs rounded-full bg-orange-100 text-orange-800">
                Expires Soon
              </span>
            )}
          </div>
        </div>
        <div className="flex space-x-2">
          <Link
            href={`/jobs/${job._id}/applications`}
            className="px-3 py-1 bg-gray-100 text-gray-700 text-sm rounded hover:bg-gray-200"
          >
            Applications
          </Link>
          <Link
            href={`/jobs/${job._id}/edit`}
            className="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700"
          >
            Edit
          </Link>
        </div>
      </div>
    </div>
  );
}

function QuickActionButton({
  href,
  icon,
  title,
  description
}: {
  href: string;
  icon: string;
  title: string;
  description: string;
}) {
  return (
    <Link
      href={href}
      className="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
    >
      <div className="text-xl mr-3">{icon}</div>
      <div>
        <p className="font-medium text-gray-900">{title}</p>
        <p className="text-sm text-gray-600">{description}</p>
      </div>
    </Link>
  );
}

function ApplicationCard({ application }: { application: RecentApplication }) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'applied': return 'bg-blue-100 text-blue-800';
      case 'under_review': return 'bg-yellow-100 text-yellow-800';
      case 'shortlisted': return 'bg-green-100 text-green-800';
      case 'rejected': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="p-3 border border-gray-200 rounded-lg hover:bg-gray-50">
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <p className="text-sm font-medium text-gray-900">
            {application.applicantId.name}
          </p>
          <p className="text-xs text-gray-600">{application.jobId.title}</p>
          <div className="flex items-center space-x-2 mt-1">
            <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(application.status)}`}>
              {application.status.replace('_', ' ')}
            </span>
            <span className="text-xs text-gray-500">
              {new Date(application.appliedAt).toLocaleDateString()}
            </span>
          </div>
        </div>
        <Link
          href={`/dashboard/organisation/applications/${application._id}`}
          className="text-blue-600 hover:text-blue-800 text-sm"
        >
          Review
        </Link>
      </div>
    </div>
  );
}
