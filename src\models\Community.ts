import mongoose, { Document, Schema } from 'mongoose';

export interface IForumPost extends Document {
  _id: string;
  title: string;
  content: string;
  authorId: string;
  category: string;
  tags: string[];
  isPinned: boolean;
  isLocked: boolean;
  views: number;
  likes: string[]; // Array of user IDs who liked
  replies: number;
  lastReplyAt?: Date;
  lastReplyBy?: string;
  status: 'active' | 'hidden' | 'deleted';
  moderationFlags: {
    flaggedBy?: string[];
    reason?: string;
    reviewedBy?: string;
    reviewedAt?: Date;
  };
  attachments?: {
    name: string;
    url: string;
    type: string;
    size: number;
  }[];
  createdAt: Date;
  updatedAt: Date;
}

export interface IForumReply extends Document {
  _id: string;
  postId: string;
  authorId: string;
  content: string;
  parentReplyId?: string; // For nested replies
  likes: string[]; // Array of user IDs who liked
  isAcceptedAnswer: boolean;
  status: 'active' | 'hidden' | 'deleted';
  moderationFlags: {
    flaggedBy?: string[];
    reason?: string;
    reviewedBy?: string;
    reviewedAt?: Date;
  };
  attachments?: {
    name: string;
    url: string;
    type: string;
    size: number;
  }[];
  editHistory?: {
    editedAt: Date;
    editedBy: string;
    previousContent: string;
  }[];
  createdAt: Date;
  updatedAt: Date;
}

export interface IForumCategory extends Document {
  _id: string;
  name: string;
  description: string;
  slug: string;
  icon?: string;
  color?: string;
  parentCategory?: string;
  order: number;
  isActive: boolean;
  moderators: string[]; // Array of user IDs
  postCount: number;
  replyCount: number;
  lastPostAt?: Date;
  lastPostBy?: string;
  permissions: {
    canPost: string[]; // Array of roles
    canReply: string[]; // Array of roles
    canView: string[]; // Array of roles
  };
  createdAt: Date;
  updatedAt: Date;
}

export interface IUserReputation extends Document {
  _id: string;
  userId: string;
  totalPoints: number;
  level: string;
  badges: {
    name: string;
    description: string;
    icon: string;
    earnedAt: Date;
  }[];
  activities: {
    type: 'post_created' | 'reply_created' | 'post_liked' | 'reply_liked' | 'answer_accepted' | 'badge_earned';
    points: number;
    description: string;
    createdAt: Date;
  }[];
  createdAt: Date;
  updatedAt: Date;
}

const ForumPostSchema = new Schema<IForumPost>({
  title: {
    type: String,
    required: true,
    trim: true,
    maxlength: 200,
  },
  content: {
    type: String,
    required: true,
    maxlength: 10000,
  },
  authorId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  category: {
    type: String,
    required: true,
  },
  tags: [{
    type: String,
    trim: true,
    lowercase: true,
  }],
  isPinned: {
    type: Boolean,
    default: false,
  },
  isLocked: {
    type: Boolean,
    default: false,
  },
  views: {
    type: Number,
    default: 0,
  },
  likes: [{
    type: Schema.Types.ObjectId,
    ref: 'User',
  }],
  replies: {
    type: Number,
    default: 0,
  },
  lastReplyAt: Date,
  lastReplyBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
  },
  status: {
    type: String,
    enum: ['active', 'hidden', 'deleted'],
    default: 'active',
  },
  moderationFlags: {
    flaggedBy: [{
      type: Schema.Types.ObjectId,
      ref: 'User',
    }],
    reason: String,
    reviewedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
    },
    reviewedAt: Date,
  },
  attachments: [{
    name: String,
    url: String,
    type: String,
    size: Number,
  }],
}, {
  timestamps: true,
});

const ForumReplySchema = new Schema<IForumReply>({
  postId: {
    type: Schema.Types.ObjectId,
    ref: 'ForumPost',
    required: true,
  },
  authorId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  content: {
    type: String,
    required: true,
    maxlength: 5000,
  },
  parentReplyId: {
    type: Schema.Types.ObjectId,
    ref: 'ForumReply',
  },
  likes: [{
    type: Schema.Types.ObjectId,
    ref: 'User',
  }],
  isAcceptedAnswer: {
    type: Boolean,
    default: false,
  },
  status: {
    type: String,
    enum: ['active', 'hidden', 'deleted'],
    default: 'active',
  },
  moderationFlags: {
    flaggedBy: [{
      type: Schema.Types.ObjectId,
      ref: 'User',
    }],
    reason: String,
    reviewedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
    },
    reviewedAt: Date,
  },
  attachments: [{
    name: String,
    url: String,
    type: String,
    size: Number,
  }],
  editHistory: [{
    editedAt: Date,
    editedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
    },
    previousContent: String,
  }],
}, {
  timestamps: true,
});

const ForumCategorySchema = new Schema<IForumCategory>({
  name: {
    type: String,
    required: true,
    trim: true,
    unique: true,
  },
  description: {
    type: String,
    required: true,
  },
  slug: {
    type: String,
    required: true,
    unique: true,
    lowercase: true,
  },
  icon: String,
  color: String,
  parentCategory: {
    type: Schema.Types.ObjectId,
    ref: 'ForumCategory',
  },
  order: {
    type: Number,
    default: 0,
  },
  isActive: {
    type: Boolean,
    default: true,
  },
  moderators: [{
    type: Schema.Types.ObjectId,
    ref: 'User',
  }],
  postCount: {
    type: Number,
    default: 0,
  },
  replyCount: {
    type: Number,
    default: 0,
  },
  lastPostAt: Date,
  lastPostBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
  },
  permissions: {
    canPost: [{
      type: String,
      enum: ['student', 'mentor', 'employer', 'admin', 'partner'],
    }],
    canReply: [{
      type: String,
      enum: ['student', 'mentor', 'employer', 'admin', 'partner'],
    }],
    canView: [{
      type: String,
      enum: ['student', 'mentor', 'employer', 'admin', 'partner'],
    }],
  },
}, {
  timestamps: true,
});

const UserReputationSchema = new Schema<IUserReputation>({
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    unique: true,
  },
  totalPoints: {
    type: Number,
    default: 0,
  },
  level: {
    type: String,
    default: 'Newcomer',
  },
  badges: [{
    name: String,
    description: String,
    icon: String,
    earnedAt: { type: Date, default: Date.now },
  }],
  activities: [{
    type: {
      type: String,
      enum: ['post_created', 'reply_created', 'post_liked', 'reply_liked', 'answer_accepted', 'badge_earned'],
    },
    points: Number,
    description: String,
    createdAt: { type: Date, default: Date.now },
  }],
}, {
  timestamps: true,
});

// Indexes
ForumPostSchema.index({ category: 1, status: 1 });
ForumPostSchema.index({ authorId: 1 });
ForumPostSchema.index({ tags: 1 });
ForumPostSchema.index({ isPinned: -1, lastReplyAt: -1 });
ForumPostSchema.index({ createdAt: -1 });

ForumReplySchema.index({ postId: 1, createdAt: 1 });
ForumReplySchema.index({ authorId: 1 });
ForumReplySchema.index({ parentReplyId: 1 });

// slug already has unique index, parentCategory + order compound index
ForumCategorySchema.index({ parentCategory: 1, order: 1 });

// userId already has unique index
UserReputationSchema.index({ totalPoints: -1 });

export const ForumPost = mongoose.models.ForumPost || mongoose.model<IForumPost>('ForumPost', ForumPostSchema);
export const ForumReply = mongoose.models.ForumReply || mongoose.model<IForumReply>('ForumReply', ForumReplySchema);
export const ForumCategory = mongoose.models.ForumCategory || mongoose.model<IForumCategory>('ForumCategory', ForumCategorySchema);
export const UserReputation = mongoose.models.UserReputation || mongoose.model<IUserReputation>('UserReputation', UserReputationSchema);
