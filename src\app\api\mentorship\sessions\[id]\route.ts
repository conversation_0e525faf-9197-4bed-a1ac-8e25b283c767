import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { connectDB } from '@/lib/mongodb';
import { MentorshipSession } from '@/models/Mentorship';
import { SMSService } from '@/lib/sms';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    await connectDB();

    const mentorshipSession = await MentorshipSession.findById(params.id)
      .populate('mentorId', 'name email avatar profile')
      .populate('menteeId', 'name email avatar profile');

    if (!mentorshipSession) {
      return NextResponse.json(
        { success: false, error: 'Session not found' },
        { status: 404 }
      );
    }

    // Check if user is part of this session
    const isParticipant = 
      mentorshipSession.mentorId._id.toString() === session.user.id ||
      mentorshipSession.menteeId._id.toString() === session.user.id;

    if (!isParticipant) {
      return NextResponse.json(
        { success: false, error: 'Access denied' },
        { status: 403 }
      );
    }

    return NextResponse.json({
      success: true,
      data: mentorshipSession,
    });
  } catch (error) {
    console.error('Error fetching mentorship session:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch session' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    await connectDB();

    const mentorshipSession = await MentorshipSession.findById(params.id);
    if (!mentorshipSession) {
      return NextResponse.json(
        { success: false, error: 'Session not found' },
        { status: 404 }
      );
    }

    // Check if user is part of this session
    const isParticipant = 
      mentorshipSession.mentorId.toString() === session.user.id ||
      mentorshipSession.menteeId.toString() === session.user.id;

    if (!isParticipant) {
      return NextResponse.json(
        { success: false, error: 'Access denied' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const {
      status,
      meetingLink,
      notes,
      feedback,
      scheduledAt,
      duration,
    } = body;

    const updateData: any = {};

    // Update status
    if (status && ['scheduled', 'in_progress', 'completed', 'cancelled', 'no_show'].includes(status)) {
      updateData.status = status;
    }

    // Update meeting link (only mentor can set this)
    if (meetingLink && mentorshipSession.mentorId.toString() === session.user.id) {
      updateData.meetingLink = meetingLink;
    }

    // Update notes
    if (notes) {
      const isMentor = mentorshipSession.mentorId.toString() === session.user.id;
      if (isMentor) {
        updateData['notes.mentorNotes'] = notes.mentorNotes || mentorshipSession.notes?.mentorNotes;
        updateData['notes.actionItems'] = notes.actionItems || mentorshipSession.notes?.actionItems;
      } else {
        updateData['notes.menteeNotes'] = notes.menteeNotes || mentorshipSession.notes?.menteeNotes;
      }
    }

    // Update feedback
    if (feedback) {
      const isMentor = mentorshipSession.mentorId.toString() === session.user.id;
      if (isMentor) {
        updateData['feedback.mentorRating'] = feedback.mentorRating;
        updateData['feedback.mentorReview'] = feedback.mentorReview;
      } else {
        updateData['feedback.menteeRating'] = feedback.menteeRating;
        updateData['feedback.menteeReview'] = feedback.menteeReview;
      }
    }

    // Update scheduling (only if session is not completed)
    if (mentorshipSession.status === 'scheduled') {
      if (scheduledAt) {
        const newScheduledDate = new Date(scheduledAt);
        if (newScheduledDate > new Date()) {
          updateData.scheduledAt = newScheduledDate;
        }
      }
      if (duration) {
        updateData.duration = duration;
      }
    }

    const updatedSession = await MentorshipSession.findByIdAndUpdate(
      params.id,
      updateData,
      { new: true }
    ).populate('mentorId', 'name email phone')
     .populate('menteeId', 'name email phone');

    // Send SMS notifications for status changes
    if (status && status !== mentorshipSession.status) {
      try {
        const mentorUser = updatedSession.mentorId as any;
        const menteeUser = updatedSession.menteeId as any;

        let smsType = '';
        let smsData = {
          mentorName: mentorUser.name,
          menteeName: menteeUser.name,
          sessionTitle: updatedSession.title,
          scheduledAt: updatedSession.scheduledAt.toLocaleString(),
          status: status,
        };

        switch (status) {
          case 'cancelled':
            smsType = 'session_cancelled';
            break;
          case 'completed':
            smsType = 'session_completed';
            break;
          case 'in_progress':
            smsType = 'session_started';
            break;
          default:
            smsType = 'session_updated';
        }

        if (mentorUser.phone) {
          await SMSService.sendSMS(mentorUser.phone, smsType, smsData);
        }
        if (menteeUser.phone) {
          await SMSService.sendSMS(menteeUser.phone, smsType, smsData);
        }
      } catch (smsError) {
        console.error('Error sending SMS notifications:', smsError);
      }
    }

    return NextResponse.json({
      success: true,
      data: updatedSession,
    });
  } catch (error) {
    console.error('Error updating mentorship session:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to update session' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    await connectDB();

    const mentorshipSession = await MentorshipSession.findById(params.id);
    if (!mentorshipSession) {
      return NextResponse.json(
        { success: false, error: 'Session not found' },
        { status: 404 }
      );
    }

    // Only allow deletion by session participants and only if not completed
    const isParticipant = 
      mentorshipSession.mentorId.toString() === session.user.id ||
      mentorshipSession.menteeId.toString() === session.user.id;

    if (!isParticipant) {
      return NextResponse.json(
        { success: false, error: 'Access denied' },
        { status: 403 }
      );
    }

    if (mentorshipSession.status === 'completed') {
      return NextResponse.json(
        { success: false, error: 'Cannot delete completed session' },
        { status: 400 }
      );
    }

    // Update status to cancelled instead of deleting
    mentorshipSession.status = 'cancelled';
    await mentorshipSession.save();

    return NextResponse.json({
      success: true,
      message: 'Session cancelled successfully',
    });
  } catch (error) {
    console.error('Error deleting mentorship session:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to delete session' },
      { status: 500 }
    );
  }
}
