import { NextRequest, NextResponse } from 'next/server';
import { connectDB } from '@/lib/mongodb';
import { Enrollment } from '@/models/Enrollment';
import { requireAuth } from '@/lib/auth-middleware';

export async function GET(request: NextRequest) {
  return requireAuth(request, async (req) => {
    try {
      await connectDB();

      const userId = req.user?.id;
      const { searchParams } = new URL(request.url);
      const limit = parseInt(searchParams.get('limit') || '10');

      // Get enrolled courses with progress
      const enrollments = await Enrollment.find({ 
        userId, 
        isActive: true 
      })
      .populate('courseId', 'title description instructor thumbnail category level duration')
      .sort({ lastAccessedAt: -1, enrolledAt: -1 })
      .limit(limit);

      // Transform data for frontend
      const enrolledCourses = enrollments.map(enrollment => {
        const course = enrollment.courseId as any;
        const progress = enrollment.progress || {};
        
        return {
          _id: course._id,
          title: course.title,
          instructor: course.instructor,
          thumbnail: course.thumbnail,
          progress: progress.completionPercentage || 0,
          nextLesson: progress.currentModule || 'Start Course',
        };
      });

      return NextResponse.json({
        success: true,
        data: enrolledCourses,
      });
    } catch (error) {
      console.error('Error fetching student courses:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to fetch enrolled courses' },
        { status: 500 }
      );
    }
  });
}
