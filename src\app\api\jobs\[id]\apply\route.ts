import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { connectDB } from '@/lib/mongodb';
import { Job } from '@/models/Job';
import { User } from '@/models/User';
import { SMSService } from '@/lib/sms';

import { JobApplication } from '@/models/JobApplication';

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    await connectDB();

    const job = await Job.findById(params.id);
    if (!job) {
      return NextResponse.json(
        { success: false, error: 'Job not found' },
        { status: 404 }
      );
    }

    // Check if job is still active
    if (job.status !== 'active') {
      return NextResponse.json(
        { success: false, error: 'Job is no longer accepting applications' },
        { status: 400 }
      );
    }

    // Check application deadline
    if (job.applicationDeadline && new Date() > job.applicationDeadline) {
      return NextResponse.json(
        { success: false, error: 'Application deadline has passed' },
        { status: 400 }
      );
    }

    // Check if user already applied
    const existingApplication = await JobApplication.findOne({
      jobId: params.id,
      applicantId: session.user.id,
    });

    if (existingApplication) {
      return NextResponse.json(
        { success: false, error: 'You have already applied for this job' },
        { status: 409 }
      );
    }

    const body = await request.json();
    const {
      coverLetter,
      resumeUrl,
      portfolioUrl,
      answers,
    } = body;

    // Validate required fields based on job requirements
    if (job.applicationRequirements?.requiresCoverLetter && !coverLetter) {
      return NextResponse.json(
        { success: false, error: 'Cover letter is required for this position' },
        { status: 400 }
      );
    }

    if (job.applicationRequirements?.requiresResume && !resumeUrl) {
      return NextResponse.json(
        { success: false, error: 'Resume is required for this position' },
        { status: 400 }
      );
    }

    if (job.applicationRequirements?.requiresPortfolio && !portfolioUrl) {
      return NextResponse.json(
        { success: false, error: 'Portfolio is required for this position' },
        { status: 400 }
      );
    }

    // Validate screening questions
    if (job.screeningQuestions && job.screeningQuestions.length > 0) {
      if (!answers || answers.length !== job.screeningQuestions.length) {
        return NextResponse.json(
          { success: false, error: 'Please answer all screening questions' },
          { status: 400 }
        );
      }
    }

    // Create application
    const application = new JobApplication({
      jobId: params.id,
      applicantId: session.user.id,
      employerId: job.employerId,
      coverLetter,
      resumeUrl,
      portfolioUrl,
      answers: answers || [],
    });

    await application.save();

    // Update job application count
    await Job.findByIdAndUpdate(params.id, {
      $inc: { 'applications.total': 1 },
    });

    // Get user and employer details for notifications
    const [applicant, employer] = await Promise.all([
      User.findById(session.user.id),
      User.findById(job.employerId),
    ]);

    // Send SMS notifications
    try {
      // Notify applicant
      if (applicant?.phone) {
        await SMSService.sendSMS(
          applicant.phone,
          'job_application_submitted',
          {
            applicantName: applicant.name,
            jobTitle: job.title,
            companyName: job.company,
            applicationId: application._id.toString(),
          }
        );
      }

      // Notify employer
      if (employer?.phone) {
        await SMSService.sendSMS(
          employer.phone,
          'new_job_application',
          {
            employerName: employer.name,
            applicantName: applicant?.name || 'Unknown',
            jobTitle: job.title,
            applicationId: application._id.toString(),
          }
        );
      }
    } catch (smsError) {
      console.error('Error sending application SMS notifications:', smsError);
    }

    // Populate application with user details
    await application.populate('applicantId', 'name email profile');

    return NextResponse.json({
      success: true,
      data: application,
      message: 'Application submitted successfully',
    }, { status: 201 });
  } catch (error) {
    console.error('Error submitting job application:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to submit application' },
      { status: 500 }
    );
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    await connectDB();

    const application = await JobApplication.findOne({
      jobId: params.id,
      applicantId: session.user.id,
    }).populate('jobId', 'title company status');

    if (!application) {
      return NextResponse.json(
        { success: false, error: 'Application not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: application,
    });
  } catch (error) {
    console.error('Error fetching job application:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch application' },
      { status: 500 }
    );
  }
}
