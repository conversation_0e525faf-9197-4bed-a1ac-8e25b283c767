import bcrypt from 'bcryptjs';
import { MongoClient } from 'mongodb';
import { config } from 'dotenv';
import path from 'path';

// Load environment variables from .env.local
config({ path: path.resolve(process.cwd(), '.env.local') });

const MONGODB_URI = process.env.MONGODB_URI!;

async function createAdminUser() {
  if (!MONGODB_URI) {
    throw new Error('Please define the MONGODB_URI environment variable');
  }

  const client = new MongoClient(MONGODB_URI);

  try {
    await client.connect();
    console.log('Connected to MongoDB');

    const db = client.db();
    const usersCollection = db.collection('users');

    // Admin user details
    const adminEmail = '<EMAIL>';
    const adminPassword = 'admin123'; // Change this to a secure password
    const adminName = 'Nova Administrator';

    // Check if admin already exists
    const existingAdmin = await usersCollection.findOne({ email: adminEmail });

    if (existingAdmin) {
      console.log('Admin user already exists with email:', adminEmail);
      return;
    }

    // Hash the password
    const hashedPassword = await bcrypt.hash(adminPassword, 12);

    // Create admin user
    const adminUser = {
      name: adminName,
      email: adminEmail,
      password: hashedPassword,
      role: 'admin',
      isActive: true,
      isVerified: true,
      profile: {
        bio: 'System Administrator',
        skills: ['Platform Management', 'User Administration'],
        interests: ['Technology', 'Education'],
        careerGoals: ['Platform Growth'],
        currentLevel: 'advanced',
      },
      preferences: {
        notifications: {
          email: true,
          sms: false,
          push: true,
        },
        language: 'en',
        timezone: 'UTC',
      },
      progress: {
        coursesCompleted: 0,
        certificatesEarned: 0,
        skillsAcquired: [],
        totalLearningHours: 0,
      },
      employment: {
        isEmployer: false,
        jobsPosted: 0,
      },
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    const result = await usersCollection.insertOne(adminUser);

    console.log('✅ Admin user created successfully!');
    console.log('📧 Email:', adminEmail);
    console.log('🔑 Password:', adminPassword);
    console.log('🆔 User ID:', result.insertedId);
    console.log('');
    console.log('⚠️  IMPORTANT: Please change the default password after first login!');
    console.log('🌐 Login URL: http://localhost:3000/auth/signin');
    console.log('📊 You will be automatically redirected to the admin dashboard after login.');

  } catch (error) {
    console.error('Error creating admin user:', error);
  } finally {
    await client.close();
    console.log('Disconnected from MongoDB');
  }
}

// Run the script
createAdminUser().catch(console.error);
