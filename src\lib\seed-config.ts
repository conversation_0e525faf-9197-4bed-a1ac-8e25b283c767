/**
 * Seed Data Configuration
 * 
 * This file contains configuration options for controlling what data gets seeded
 * during database initialization. This is particularly useful for testing scenarios
 * where you want to skip certain resource-intensive or complex entity creation.
 */

export interface SeedConfig {
  /** Skip creating resources during seeding */
  skipResources: boolean;
  
  /** Skip creating mentor profiles during seeding */
  skipMentorProfiles: boolean;
  
  /** Minimal mode - only create essential data (users, basic courses) */
  minimalMode: boolean;
  
  /** Skip creating jobs */
  skipJobs: boolean;
  
  /** Skip creating events */
  skipEvents: boolean;
  
  /** Skip creating courses */
  skipCourses: boolean;
}

/**
 * Get seed configuration from environment variables
 */
export function getSeedConfig(): SeedConfig {
  const config: SeedConfig = {
    skipResources: process.env.SEED_SKIP_RESOURCES === 'true',
    skipMentorProfiles: process.env.SEED_SKIP_MENTOR_PROFILES === 'true',
    minimalMode: process.env.SEED_MINIMAL_MODE === 'true',
    skipJobs: false,
    skipEvents: false,
    skipCourses: false,
  };

  // If minimal mode is enabled, skip everything except users and basic courses
  if (config.minimalMode) {
    config.skipResources = true;
    config.skipMentorProfiles = true;
    config.skipJobs = true;
    config.skipEvents = true;
    // Keep courses for basic functionality
  }

  return config;
}

/**
 * Default configuration for testing environments
 */
export const TEST_CONFIG: SeedConfig = {
  skipResources: true,
  skipMentorProfiles: true,
  minimalMode: false,
  skipJobs: false,
  skipEvents: false,
  skipCourses: false,
};

/**
 * Default configuration for development environments
 */
export const DEVELOPMENT_CONFIG: SeedConfig = {
  skipResources: false,
  skipMentorProfiles: false,
  minimalMode: false,
  skipJobs: false,
  skipEvents: false,
  skipCourses: false,
};

/**
 * Get configuration based on environment
 */
export function getEnvironmentConfig(): SeedConfig {
  const nodeEnv = process.env.NODE_ENV;
  
  if (nodeEnv === 'test') {
    return { ...TEST_CONFIG, ...getSeedConfig() };
  }
  
  return getSeedConfig();
}

/**
 * Log the current seed configuration
 */
export function logSeedConfig(config: SeedConfig): void {
  console.log('\n🔧 Seed Configuration:');
  console.log(`   Skip Resources: ${config.skipResources ? '✅' : '❌'}`);
  console.log(`   Skip Mentor Profiles: ${config.skipMentorProfiles ? '✅' : '❌'}`);
  console.log(`   Skip Jobs: ${config.skipJobs ? '✅' : '❌'}`);
  console.log(`   Skip Events: ${config.skipEvents ? '✅' : '❌'}`);
  console.log(`   Skip Courses: ${config.skipCourses ? '✅' : '❌'}`);
  console.log(`   Minimal Mode: ${config.minimalMode ? '✅' : '❌'}`);
  console.log('');
}
