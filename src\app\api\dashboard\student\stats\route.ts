import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { connectDB } from '@/lib/mongodb';
import { Enrollment } from '@/models/Enrollment';
import { Certificate } from '@/models/Certificate';
import { MentorshipSession } from '@/models/Mentorship';
import { requireAuth } from '@/lib/auth-middleware';

export async function GET(request: NextRequest) {
  return requireAuth(request, async (req) => {
    try {
      await connectDB();

      const userId = req.user?.id;

      // Get enrollment statistics
      const [
        totalEnrollments,
        completedEnrollments,
        certificates,
        mentorshipSessions,
        jobApplications,
      ] = await Promise.all([
        Enrollment.countDocuments({ userId, isActive: true }),
        Enrollment.countDocuments({ userId, isCompleted: true }),
        Certificate.countDocuments({ userId, status: 'issued' }),
        // Note: You'll need to import JobApplication model
        // JobApplication.countDocuments({ applicantId: userId }),
        Promise.resolve(0), // Placeholder for job applications
        // MentorshipSession.countDocuments({ 
        //   $or: [{ mentorId: userId }, { menteeId: userId }] 
        // }),
        Promise.resolve(0), // Placeholder for mentorship sessions
      ]);

      // Calculate total learning hours from enrollments
      const enrollmentsWithProgress = await Enrollment.find({ 
        userId, 
        isActive: true 
      }).select('progress.timeSpent');

      const totalLearningHours = enrollmentsWithProgress.reduce((total, enrollment) => {
        return total + (enrollment.progress?.timeSpent || 0);
      }, 0);

      const stats = {
        coursesEnrolled: totalEnrollments,
        coursesCompleted: completedEnrollments,
        certificatesEarned: certificates,
        jobApplications: jobApplications,
        mentorshipSessions: mentorshipSessions,
        learningHours: Math.round(totalLearningHours / 60), // Convert minutes to hours
      };

      return NextResponse.json({
        success: true,
        data: stats,
      });
    } catch (error) {
      console.error('Error fetching student stats:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to fetch student statistics' },
        { status: 500 }
      );
    }
  });
}
