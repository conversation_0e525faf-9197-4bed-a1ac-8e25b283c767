import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import connectDB from '@/lib/mongodb';
import { User } from '@/models/User';
import { Course } from '@/models/Course';
import { Job } from '@/models/Job';
import { Event } from '@/models/Event';
import { MentorshipSession } from '@/models/Mentorship';

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    await connectDB();

    // Check if user is admin
    const user = await User.findById(session.user.id);
    if (!user || user.role !== 'admin') {
      return NextResponse.json(
        { success: false, error: 'Access denied' },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '10');

    const now = new Date();
    const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

    // Generate system alerts
    const alerts = [];

    // Check for inactive users (no login in 30 days)
    const inactiveUsersCount = await User.countDocuments({
      lastLogin: { $lt: new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000) },
      isActive: true
    });

    if (inactiveUsersCount > 0) {
      alerts.push({
        id: 'inactive-users',
        type: 'warning',
        title: 'Inactive Users Detected',
        message: `${inactiveUsersCount} users haven't logged in for 30+ days`,
        priority: 'medium',
        createdAt: now,
        actionRequired: true,
        category: 'user_engagement'
      });
    }

    // Check for courses with low enrollment
    const lowEnrollmentCourses = await Course.aggregate([
      { $match: { isPublished: true } },
      {
        $lookup: {
          from: 'enrollments',
          localField: '_id',
          foreignField: 'courseId',
          as: 'enrollments'
        }
      },
      {
        $addFields: {
          enrollmentCount: { $size: '$enrollments' }
        }
      },
      { $match: { enrollmentCount: { $lt: 5 } } },
      { $count: 'total' }
    ]);

    const lowEnrollmentCount = lowEnrollmentCourses[0]?.total || 0;
    if (lowEnrollmentCount > 0) {
      alerts.push({
        id: 'low-enrollment',
        type: 'info',
        title: 'Low Course Enrollment',
        message: `${lowEnrollmentCount} courses have less than 5 enrollments`,
        priority: 'low',
        createdAt: now,
        actionRequired: false,
        category: 'course_performance'
      });
    }

    // Check for expired job postings
    const expiredJobs = await Job.countDocuments({
      applicationDeadline: { $lt: now },
      isActive: true
    });

    if (expiredJobs > 0) {
      alerts.push({
        id: 'expired-jobs',
        type: 'warning',
        title: 'Expired Job Postings',
        message: `${expiredJobs} job postings have passed their deadline`,
        priority: 'medium',
        createdAt: now,
        actionRequired: true,
        category: 'job_management'
      });
    }

    // Check for upcoming events without registrations
    const upcomingEventsWithoutRegistrations = await Event.aggregate([
      {
        $match: {
          'schedule.startDate': { $gte: now, $lte: new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000) },
          status: 'published'
        }
      },
      {
        $addFields: {
          registrationCount: { $size: '$attendees' }
        }
      },
      { $match: { registrationCount: 0 } },
      { $count: 'total' }
    ]);

    const noRegistrationEvents = upcomingEventsWithoutRegistrations[0]?.total || 0;
    if (noRegistrationEvents > 0) {
      alerts.push({
        id: 'no-registrations',
        type: 'warning',
        title: 'Events Without Registrations',
        message: `${noRegistrationEvents} upcoming events have no registrations`,
        priority: 'medium',
        createdAt: now,
        actionRequired: true,
        category: 'event_management'
      });
    }

    // Check for pending mentorship sessions
    const pendingSessions = await MentorshipSession.countDocuments({
      status: 'pending',
      scheduledAt: { $gte: now }
    });

    if (pendingSessions > 0) {
      alerts.push({
        id: 'pending-sessions',
        type: 'info',
        title: 'Pending Mentorship Sessions',
        message: `${pendingSessions} mentorship sessions are pending confirmation`,
        priority: 'low',
        createdAt: now,
        actionRequired: false,
        category: 'mentorship'
      });
    }

    // Check for high user growth (potential scaling issues)
    const recentUsers = await User.countDocuments({
      createdAt: { $gte: oneDayAgo }
    });

    if (recentUsers > 50) {
      alerts.push({
        id: 'high-growth',
        type: 'success',
        title: 'High User Growth',
        message: `${recentUsers} new users registered in the last 24 hours`,
        priority: 'high',
        createdAt: now,
        actionRequired: false,
        category: 'growth'
      });
    }

    // Check for system health (placeholder for actual monitoring)
    alerts.push({
      id: 'system-health',
      type: 'success',
      title: 'System Status',
      message: 'All systems operational',
      priority: 'low',
      createdAt: now,
      actionRequired: false,
      category: 'system'
    });

    // Sort alerts by priority and creation date
    const priorityOrder = { high: 3, medium: 2, low: 1 };
    const sortedAlerts = alerts
      .sort((a, b) => {
        const priorityDiff = priorityOrder[b.priority] - priorityOrder[a.priority];
        if (priorityDiff !== 0) return priorityDiff;
        return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
      })
      .slice(0, limit);

    return NextResponse.json({
      success: true,
      data: {
        alerts: sortedAlerts,
        summary: {
          total: sortedAlerts.length,
          critical: sortedAlerts.filter(a => a.priority === 'high').length,
          warnings: sortedAlerts.filter(a => a.priority === 'medium').length,
          info: sortedAlerts.filter(a => a.priority === 'low').length,
          actionRequired: sortedAlerts.filter(a => a.actionRequired).length
        }
      }
    });
  } catch (error) {
    console.error('Error fetching alerts:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch alerts' },
      { status: 500 }
    );
  }
}
