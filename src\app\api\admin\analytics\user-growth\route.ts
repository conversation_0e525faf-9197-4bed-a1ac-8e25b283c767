import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import connectDB from '@/lib/mongodb';
import { User } from '@/models/User';

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    await connectDB();

    // Check if user is admin
    const user = await User.findById(session.user.id);
    if (!user || user.role !== 'admin') {
      return NextResponse.json(
        { success: false, error: 'Access denied' },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const days = parseInt(searchParams.get('days') || '30');

    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    // Get user growth data
    const userGrowthData = await User.aggregate([
      {
        $match: {
          createdAt: { $gte: startDate, $lte: endDate },
          isActive: true
        }
      },
      {
        $group: {
          _id: {
            $dateToString: { format: '%Y-%m-%d', date: '$createdAt' }
          },
          count: { $sum: 1 },
          roles: {
            $push: '$role'
          }
        }
      },
      {
        $addFields: {
          students: {
            $size: {
              $filter: {
                input: '$roles',
                cond: { $eq: ['$$this', 'student'] }
              }
            }
          },
          mentors: {
            $size: {
              $filter: {
                input: '$roles',
                cond: { $eq: ['$$this', 'mentor'] }
              }
            }
          },
          employers: {
            $size: {
              $filter: {
                input: '$roles',
                cond: { $eq: ['$$this', 'employer'] }
              }
            }
          },
          partners: {
            $size: {
              $filter: {
                input: '$roles',
                cond: { $eq: ['$$this', 'partner'] }
              }
            }
          }
        }
      },
      {
        $project: {
          date: '$_id',
          total: '$count',
          students: 1,
          mentors: 1,
          employers: 1,
          partners: 1,
          _id: 0
        }
      },
      { $sort: { date: 1 } }
    ]);

    // Fill in missing dates with zero values
    const filledData = [];
    const currentDate = new Date(startDate);

    while (currentDate <= endDate) {
      const dateString = currentDate.toISOString().split('T')[0];
      const existingData = userGrowthData.find(d => d.date === dateString);

      filledData.push({
        date: dateString,
        total: existingData?.total || 0,
        students: existingData?.students || 0,
        mentors: existingData?.mentors || 0,
        employers: existingData?.employers || 0,
        partners: existingData?.partners || 0
      });

      currentDate.setDate(currentDate.getDate() + 1);
    }

    // Calculate cumulative totals
    let cumulativeTotal = 0;
    let cumulativeStudents = 0;
    let cumulativeMentors = 0;
    let cumulativeEmployers = 0;
    let cumulativePartners = 0;

    const cumulativeData = filledData.map(day => {
      cumulativeTotal += day.total;
      cumulativeStudents += day.students;
      cumulativeMentors += day.mentors;
      cumulativeEmployers += day.employers;
      cumulativePartners += day.partners;

      return {
        ...day,
        cumulativeTotal,
        cumulativeStudents,
        cumulativeMentors,
        cumulativeEmployers,
        cumulativePartners
      };
    });

    // Calculate growth metrics
    const totalNewUsers = userGrowthData.reduce((sum, day) => sum + day.total, 0);
    const averageDailyGrowth = totalNewUsers / days;

    // Calculate growth rate (comparing first half vs second half of period)
    const midPoint = Math.floor(days / 2);
    const firstHalf = filledData.slice(0, midPoint);
    const secondHalf = filledData.slice(midPoint);

    const firstHalfTotal = firstHalf.reduce((sum, day) => sum + day.total, 0);
    const secondHalfTotal = secondHalf.reduce((sum, day) => sum + day.total, 0);

    const growthRate = firstHalfTotal > 0
      ? ((secondHalfTotal - firstHalfTotal) / firstHalfTotal) * 100
      : 0;

    // Get role distribution for the period
    const roleDistribution = await User.aggregate([
      {
        $match: {
          createdAt: { $gte: startDate, $lte: endDate },
          isActive: true
        }
      },
      {
        $group: {
          _id: '$role',
          count: { $sum: 1 }
        }
      }
    ]);

    return NextResponse.json({
      success: true,
      data: {
        period: {
          startDate: startDate.toISOString(),
          endDate: endDate.toISOString(),
          days
        },
        dailyGrowth: filledData,
        cumulativeGrowth: cumulativeData,
        metrics: {
          totalNewUsers,
          averageDailyGrowth: Math.round(averageDailyGrowth * 100) / 100,
          growthRate: Math.round(growthRate * 100) / 100,
          peakDay: filledData.reduce((max, day) =>
            day.total > max.total ? day : max, filledData[0]
          )
        },
        roleDistribution: roleDistribution.reduce((acc, role) => {
          acc[role._id] = role.count;
          return acc;
        }, {})
      }
    });
  } catch (error) {
    console.error('Error fetching user growth analytics:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch user growth analytics' },
      { status: 500 }
    );
  }
}
