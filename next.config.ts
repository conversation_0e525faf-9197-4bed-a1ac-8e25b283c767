import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  serverExternalPackages: ['mongodb', 'mongoose'],
  webpack: (config, { isServer }) => {
    if (!isServer) {
      // Don't resolve 'fs' module on the client to prevent this error on build --> Error: Can't resolve 'fs'
      config.resolve.fallback = {
        fs: false,
        net: false,
        dns: false,
        tls: false,
        'child_process': false,
        'fs/promises': false,
        'timers/promises': false,
        // MongoDB optional dependencies
        'kerberos': false,
        '@mongodb-js/zstd': false,
        '@aws-sdk/credential-providers': false,
        'gcp-metadata': false,
        'snappy': false,
        'socks': false,
        'aws4': false,
        'mongodb-client-encryption': false,
      };
    }
    return config;
  },
};

export default nextConfig;
