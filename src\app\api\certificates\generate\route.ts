import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { connectDB } from '@/lib/mongodb';
import { Certificate } from '@/models/Certificate';
import { Course } from '@/models/Course';
import { Enrollment } from '@/models/Enrollment';
import { User } from '@/models/User';
import { SMSService } from '@/lib/sms';
import { v4 as uuidv4 } from 'uuid';

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    await connectDB();

    const body = await request.json();
    const { 
      courseId, 
      type = 'course_completion',
      assessmentScore,
      assessmentMaxScore,
      passingScore = 70,
    } = body;

    // Validate required fields
    if (!courseId) {
      return NextResponse.json(
        { success: false, error: 'Course ID is required' },
        { status: 400 }
      );
    }

    // Get course details
    const course = await Course.findById(courseId);
    if (!course) {
      return NextResponse.json(
        { success: false, error: 'Course not found' },
        { status: 404 }
      );
    }

    // Get enrollment details
    const enrollment = await Enrollment.findOne({
      userId: session.user.id,
      courseId,
    });

    if (!enrollment) {
      return NextResponse.json(
        { success: false, error: 'Enrollment not found' },
        { status: 404 }
      );
    }

    // Check if course is completed
    if (!enrollment.isCompleted) {
      return NextResponse.json(
        { success: false, error: 'Course not completed' },
        { status: 400 }
      );
    }

    // Check if certificate already exists
    const existingCertificate = await Certificate.findOne({
      userId: session.user.id,
      courseId,
      type,
    });

    if (existingCertificate) {
      return NextResponse.json(
        { success: false, error: 'Certificate already exists' },
        { status: 409 }
      );
    }

    // Get user details
    const user = await User.findById(session.user.id);
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'User not found' },
        { status: 404 }
      );
    }

    // Calculate assessment details
    const score = assessmentScore || enrollment.finalScore || 0;
    const maxScore = assessmentMaxScore || 100;
    const percentage = Math.round((score / maxScore) * 100);

    // Check if passing score is met
    if (percentage < passingScore) {
      return NextResponse.json(
        { success: false, error: `Minimum score of ${passingScore}% required for certification` },
        { status: 400 }
      );
    }

    // Generate unique certificate ID
    const certificateId = `NOVA-${Date.now()}-${uuidv4().substring(0, 8).toUpperCase()}`;

    // Determine skill level based on score
    let skillLevel: 'beginner' | 'intermediate' | 'advanced' | 'expert' = 'beginner';
    if (percentage >= 95) skillLevel = 'expert';
    else if (percentage >= 85) skillLevel = 'advanced';
    else if (percentage >= 75) skillLevel = 'intermediate';

    // Create certificate
    const certificate = new Certificate({
      certificateId,
      userId: session.user.id,
      courseId,
      type,
      title: `Certificate of Completion - ${course.title}`,
      description: `This certifies that ${user.name} has successfully completed the course "${course.title}" and demonstrated proficiency in the subject matter.`,
      issuer: {
        name: 'Nova Platform',
        organization: 'Nova - Space Technology Learning Platform',
        logo: '/logo.png',
      },
      recipient: {
        name: user.name,
        email: user.email,
        userId: session.user.id,
      },
      skills: course.tags || [],
      competencies: course.learningOutcomes?.map(outcome => ({
        name: outcome,
        level: skillLevel,
        verified: true,
      })) || [],
      assessment: {
        score,
        maxScore,
        percentage,
        passingScore,
        assessmentDate: enrollment.completedAt || new Date(),
        assessmentType: 'comprehensive',
      },
      verification: {
        verificationUrl: `${process.env.NEXTAUTH_URL}/verify/${certificateId}`,
        isVerified: true,
        verifiedAt: new Date(),
        verificationMethod: 'database',
      },
      metadata: {
        duration: course.duration,
        completionDate: enrollment.completedAt || new Date(),
        level: skillLevel,
        category: course.category,
      },
      design: {
        templateId: 'default',
        backgroundColor: '#ffffff',
        textColor: '#000000',
        logoPosition: 'top',
      },
      sharing: {
        isPublic: true,
        portfolioIncluded: true,
      },
      status: 'issued',
    });

    await certificate.save();

    // Update enrollment with certificate info
    enrollment.certificate.issued = true;
    enrollment.certificate.issuedAt = new Date();
    enrollment.certificate.certificateId = certificateId;
    enrollment.certificate.downloadUrl = `/api/certificates/${certificate._id}/download`;
    await enrollment.save();

    // Update user progress
    await User.findByIdAndUpdate(session.user.id, {
      $inc: { 'progress.certificatesEarned': 1 },
      $addToSet: { 'progress.skillsAcquired': { $each: course.tags || [] } },
    });

    // Send SMS notification
    try {
      if (user.phone) {
        await SMSService.sendSMS(
          user.phone,
          'certification',
          {
            userName: user.name,
            courseTitle: course.title,
            certificateId,
            verificationUrl: certificate.verification.verificationUrl,
          }
        );
      }
    } catch (smsError) {
      console.error('Error sending certificate SMS:', smsError);
    }

    return NextResponse.json({
      success: true,
      data: certificate,
      message: 'Certificate generated successfully',
    }, { status: 201 });
  } catch (error) {
    console.error('Error generating certificate:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to generate certificate' },
      { status: 500 }
    );
  }
}
