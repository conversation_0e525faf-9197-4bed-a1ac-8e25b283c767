@import "tailwindcss";
@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;600;700;800;900&family=Space+Grotesk:wght@300;400;500;600;700&display=swap');

:root {
  /* Dark Galaxy Theme Colors - Dark Blue, Dark Purple, Black */
  --galaxy-black: #000000;
  --galaxy-dark: #0a0a0f;
  --galaxy-deep: #1a1a2e;
  --galaxy-purple: #2d1b69;
  --galaxy-purple-light: #4c2a85;
  --galaxy-purple-dark: #1a0f3d;
  --galaxy-blue: #1e3a8a;
  --galaxy-blue-light: #3b82f6;
  --galaxy-blue-dark: #0f1f5c;

  /* Refined Cosmic Gradients - Dark Blue & Purple Focus */
  --cosmic-gradient: linear-gradient(135deg, #000000 0%, #0f1f5c 25%, #2d1b69 50%, #1e3a8a 75%, #000000 100%);
  --nebula-gradient: linear-gradient(45deg, #1a0f3d 0%, #2d1b69 50%, #1e3a8a 100%);
  --aurora-gradient: linear-gradient(90deg, #1e3a8a 0%, #2d1b69 50%, #0f1f5c 100%);

  /* Theme Variables */
  --background: var(--galaxy-black);
  --foreground: #ffffff;
  --text-muted: #a0a0a0;
  --border-glow: rgba(30, 58, 138, 0.4);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --font-space: 'Space Grotesk', sans-serif;
  --font-orbitron: 'Orbitron', monospace;
}

body {
  background: #000000;
  color: var(--foreground);
  font-family: 'Space Grotesk', var(--font-geist-sans), sans-serif;
  overflow-x: hidden;
}

/* Cosmic Animations */
@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

@keyframes pulse-glow {
  0%, 100% { box-shadow: 0 0 20px rgba(30, 58, 138, 0.4); }
  50% { box-shadow: 0 0 40px rgba(30, 58, 138, 0.7), 0 0 60px rgba(45, 27, 105, 0.4); }
}

@keyframes twinkle {
  0%, 100% { opacity: 0.3; transform: scale(1); }
  50% { opacity: 1; transform: scale(1.2); }
}

@keyframes cosmic-drift {
  0% { transform: translateX(-100px) translateY(0px) rotate(0deg); }
  25% { transform: translateX(0px) translateY(-50px) rotate(90deg); }
  50% { transform: translateX(100px) translateY(0px) rotate(180deg); }
  75% { transform: translateX(0px) translateY(50px) rotate(270deg); }
  100% { transform: translateX(-100px) translateY(0px) rotate(360deg); }
}

@keyframes aurora-flow {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

@keyframes parallax-stars {
  0% { transform: translateY(0px); }
  100% { transform: translateY(-100px); }
}

/* Utility Classes */
.cosmic-gradient {
  background: #000000;
}

.nebula-gradient {
  background: var(--nebula-gradient);
  background-size: 400% 400%;
  animation: aurora-flow 8s ease-in-out infinite;
}

.aurora-gradient {
  background: var(--aurora-gradient);
  background-size: 200% 200%;
  animation: aurora-flow 6s ease-in-out infinite;
}

.glassmorphic {
  background: rgba(255, 255, 255, 0.03);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.08);
}

/* Muted Matted Glassmorphic Background Effects */
.galaxy-background {
  background: #000000;
  position: relative;
}

.galaxy-background::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 30%, rgba(30, 58, 138, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 80% 70%, rgba(45, 27, 105, 0.06) 0%, transparent 50%),
    radial-gradient(circle at 40% 80%, rgba(30, 58, 138, 0.04) 0%, transparent 50%),
    radial-gradient(circle at 90% 20%, rgba(45, 27, 105, 0.05) 0%, transparent 50%);
  backdrop-filter: blur(1px);
  pointer-events: none;
}

.muted-glassmorphic {
  background: rgba(255, 255, 255, 0.01);
  backdrop-filter: blur(4px);
  border: 1px solid rgba(255, 255, 255, 0.04);
}

.dark-blur-gradient {
  background:
    linear-gradient(135deg,
      rgba(0, 0, 0, 0.9) 0%,
      rgba(15, 31, 92, 0.1) 25%,
      rgba(45, 27, 105, 0.08) 50%,
      rgba(30, 58, 138, 0.06) 75%,
      rgba(0, 0, 0, 0.95) 100%
    );
  backdrop-filter: blur(2px);
}

.glow-border {
  border: 1px solid var(--border-glow);
  box-shadow: 0 0 15px rgba(59, 130, 246, 0.15);
}

.glow-text {
  text-shadow:
    0 0 10px rgba(59, 130, 246, 0.6),
    0 0 20px rgba(147, 51, 234, 0.4),
    0 0 30px rgba(219, 39, 119, 0.3);
}

/* Galaxy Text Effects - Direct text styling instead of background */
.galaxy-text-blue {
  color: #3b82f6;
  text-shadow:
    0 0 10px rgba(59, 130, 246, 0.8),
    0 0 20px rgba(59, 130, 246, 0.6),
    0 0 30px rgba(59, 130, 246, 0.4);
}

.galaxy-text-purple {
  color: #a855f7;
  text-shadow:
    0 0 10px rgba(168, 85, 247, 0.8),
    0 0 20px rgba(168, 85, 247, 0.6),
    0 0 30px rgba(168, 85, 247, 0.4);
}

.galaxy-text-pink {
  color: #ec4899;
  text-shadow:
    0 0 10px rgba(236, 72, 153, 0.8),
    0 0 20px rgba(236, 72, 153, 0.6),
    0 0 30px rgba(236, 72, 153, 0.4);
}

.galaxy-text-gradient {
  background: linear-gradient(45deg, #3b82f6, #a855f7, #ec4899);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: none;
  filter: drop-shadow(0 0 10px rgba(59, 130, 246, 0.5));
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-pulse-glow {
  animation: pulse-glow 3s ease-in-out infinite;
}

.animate-twinkle {
  animation: twinkle 2s ease-in-out infinite;
}

.animate-cosmic-drift {
  animation: cosmic-drift 20s linear infinite;
}

.animation-delay-1000 { animation-delay: 1s; }
.animation-delay-2000 { animation-delay: 2s; }
.animation-delay-3000 { animation-delay: 3s; }
.animation-delay-4000 { animation-delay: 4s; }
.animation-delay-5000 { animation-delay: 5s; }

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom scrollbar - Dark Galaxy Theme */
::-webkit-scrollbar {
  width: 12px;
}

::-webkit-scrollbar-track {
  background: var(--galaxy-dark);
  border-radius: 6px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(45deg, var(--galaxy-cyan), var(--galaxy-pink));
  border-radius: 6px;
  border: 2px solid var(--galaxy-dark);
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(45deg, var(--galaxy-pink), var(--galaxy-gold));
  box-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
}

/* Star Field Background */
.star-field {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: -1;
}

.star-field::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(2px 2px at 20px 30px, #fff, transparent),
    radial-gradient(2px 2px at 40px 70px, rgba(255,255,255,0.8), transparent),
    radial-gradient(1px 1px at 90px 40px, #fff, transparent),
    radial-gradient(1px 1px at 130px 80px, rgba(255,255,255,0.6), transparent),
    radial-gradient(2px 2px at 160px 30px, #fff, transparent);
  background-repeat: repeat;
  background-size: 200px 100px;
  animation: parallax-stars 20s linear infinite;
}

/* Parallax Container */
.parallax-container {
  position: relative;
  overflow: hidden;
}

/* Section Margins */
.section-container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 2rem;
}

@media (max-width: 768px) {
  .section-container {
    padding: 0 1rem;
  }
}

/* Bento Box Grid Layouts */
.bento-grid {
  display: grid;
  gap: 1.5rem;
  grid-auto-rows: minmax(200px, auto);
}

.bento-grid-features {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.bento-grid-testimonials {
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
}

.bento-item {
  border-radius: 1rem;
  padding: 1.5rem;
  transition: all 0.3s ease;
}

.bento-item-large {
  grid-row: span 2;
}

.bento-item-wide {
  grid-column: span 2;
}

.bento-item-tall {
  grid-row: span 2;
}

@media (min-width: 768px) {
  .bento-grid-features {
    grid-template-columns: repeat(4, 1fr);
  }

  .bento-grid-testimonials {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1024px) {
  .bento-grid {
    gap: 2rem;
  }

  .bento-item {
    padding: 2rem;
  }
}

/* Navbar and Footer Margins */
.nav-container, .footer-container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* Footer Rounded Edges and Margins */
.footer-rounded {
  margin: 2rem;
  border-radius: 2rem;
  overflow: hidden;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

@media (max-width: 768px) {
  .nav-container, .footer-container {
    padding: 0 1rem;
  }

  .footer-rounded {
    margin: 1rem;
    border-radius: 1.5rem;
  }
}
