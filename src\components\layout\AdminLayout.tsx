'use client';

import React from 'react';
import { AdminNavbar } from './AdminNavbar';

interface AdminLayoutProps {
  children: React.ReactNode;
  pendingApprovals?: number;
  systemAlerts?: number;
}

export function AdminLayout({ children, pendingApprovals, systemAlerts }: AdminLayoutProps) {
  return (
    <div className="min-h-screen bg-gray-50">
      <AdminNavbar pendingApprovals={pendingApprovals} systemAlerts={systemAlerts} />
      
      {/* Main content */}
      <div className="lg:ml-64">
        <main className="p-4 lg:p-8">
          {children}
        </main>
      </div>
    </div>
  );
}
