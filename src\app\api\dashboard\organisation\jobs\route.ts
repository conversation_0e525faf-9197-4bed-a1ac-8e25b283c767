import { NextRequest, NextResponse } from 'next/server';
import { connectDB } from '@/lib/mongodb';
import { Job } from '@/models/Job';
import { requireAuth } from '@/lib/auth-middleware';

export async function GET(request: NextRequest) {
  return requireAuth(request, async (req) => {
    // Check if user is an organisation
    if (req.user?.role !== 'organisation' && req.user?.role !== 'admin') {
      return NextResponse.json(
        { success: false, error: 'Access denied. Organisation role required.' },
        { status: 403 }
      );
    }
    try {
      await connectDB();

      const userId = req.user?.id;
      const { searchParams } = new URL(request.url);
      const limit = parseInt(searchParams.get('limit') || '10');
      const page = parseInt(searchParams.get('page') || '1');
      const skip = (page - 1) * limit;

      // Get employer's job postings
      const jobs = await Job.find({
        employerId: userId
      })
      .select('title location type applications createdAt applicationDeadline isActive')
      .sort({ createdAt: -1 })
      .limit(limit)
      .skip(skip)
      .lean();

      // Transform the data to match frontend expectations
      const transformedJobs = jobs.map(job => ({
        _id: job._id.toString(),
        title: job.title,
        location: typeof job.location === 'object' && job.location.type
          ? `${job.location.type}${job.location.city ? ` - ${job.location.city}` : ''}`
          : job.location || 'Not specified',
        type: job.type,
        applicationsCount: job.applications?.total || 0,
        status: job.isActive ? 'active' : 'inactive',
        createdAt: job.createdAt,
        deadline: job.applicationDeadline,
      }));

      return NextResponse.json({
        success: true,
        data: transformedJobs,
      });
    } catch (error) {
      console.error('Error fetching employer jobs:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to fetch employer jobs' },
        { status: 500 }
      );
    }
  });
}
