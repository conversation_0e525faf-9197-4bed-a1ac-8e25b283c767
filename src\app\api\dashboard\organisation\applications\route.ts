import { NextRequest, NextResponse } from 'next/server';
import { connectDB } from '@/lib/mongodb';
import { JobApplication } from '@/models/JobApplication';
import { requireAuth } from '@/lib/auth-middleware';

export async function GET(request: NextRequest) {
  return requireAuth(request, async (req) => {
    // Check if user is an organisation
    if (req.user?.role !== 'organisation' && req.user?.role !== 'admin') {
      return NextResponse.json(
        { success: false, error: 'Access denied. Organisation role required.' },
        { status: 403 }
      );
    }
    try {
      await connectDB();

      const userId = req.user?.id;
      const { searchParams } = new URL(request.url);
      const limit = parseInt(searchParams.get('limit') || '10');
      const page = parseInt(searchParams.get('page') || '1');
      const skip = (page - 1) * limit;

      // Get recent applications for employer's jobs
      const applications = await JobApplication.find({
        employerId: userId
      })
      .populate('jobId', 'title')
      .populate('applicantId', 'name email profile.avatar')
      .sort({ appliedAt: -1 })
      .limit(limit)
      .skip(skip)
      .lean();

      // Transform the data to match frontend expectations
      const transformedApplications = applications.map(app => ({
        _id: app._id.toString(),
        jobId: {
          _id: app.jobId?._id?.toString() || '',
          title: app.jobId?.title || 'Unknown Job',
        },
        applicantId: {
          name: app.applicantId?.name || 'Unknown Applicant',
          email: app.applicantId?.email || '',
          avatar: app.applicantId?.profile?.avatar || null,
        },
        status: app.status,
        appliedAt: app.appliedAt,
        coverLetter: app.coverLetter,
      }));

      return NextResponse.json({
        success: true,
        data: transformedApplications,
      });
    } catch (error) {
      console.error('Error fetching employer applications:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to fetch employer applications' },
        { status: 500 }
      );
    }
  });
}
