'use client';

import React from 'react';
import { motion } from 'framer-motion';
import Link from 'next/link';
import { <PERSON>R<PERSON>, Play, Star, Users, BookOpen, Award, Sparkles, Zap } from 'lucide-react';

export function Hero() {
  return (
    <section className="relative pt-36 sm:pt-40 pb-20 overflow-hidden parallax-container">
      {/* Cosmic Background Elements */}
      <div className="absolute inset-0">
        {/* Floating cosmic elements */}
        <div className="absolute top-20 left-10 w-96 h-96 bg-gradient-to-r from-blue-600/15 to-pink-600/15 rounded-full filter blur-3xl animate-cosmic-drift" />
        <div className="absolute top-40 right-10 w-80 h-80 bg-gradient-to-r from-purple-600/15 to-blue-600/15 rounded-full filter blur-3xl animate-cosmic-drift animation-delay-2000" />
        <div className="absolute -bottom-8 left-20 w-72 h-72 bg-gradient-to-r from-pink-600/15 to-purple-600/15 rounded-full filter blur-3xl animate-cosmic-drift animation-delay-4000" />

        {/* Twinkling stars */}
        <div className="absolute top-32 left-1/4 w-2 h-2 bg-blue-400 rounded-full animate-twinkle" />
        <div className="absolute top-48 right-1/3 w-1 h-1 bg-pink-400 rounded-full animate-twinkle animation-delay-1000" />
        <div className="absolute bottom-32 left-1/3 w-1.5 h-1.5 bg-purple-400 rounded-full animate-twinkle animation-delay-3000" />
        <div className="absolute top-64 right-1/4 w-1 h-1 bg-blue-300 rounded-full animate-twinkle animation-delay-2000" />
      </div>

      <div className="relative section-container">
        <div className="grid lg:grid-cols-2 gap-16 items-center">
          {/* Left Column - Content */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center lg:text-left"
          >
            {/* Badge */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="inline-flex items-center px-6 py-3 rounded-full muted-glassmorphic text-blue-400 text-sm font-medium mb-8"
            >
              <Sparkles className="h-5 w-5 mr-2 animate-twinkle" />
              Empowering Africa's Space Tech Future
            </motion.div>

            {/* Main Heading */}
            <motion.h1
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="text-5xl md:text-6xl lg:text-7xl font-bold text-white mb-8 font-orbitron"
            >
              Launch Your
              <span className="galaxy-text-gradient">
                {" "}Space Tech{" "}
              </span>
              Career
            </motion.h1>

            {/* Subtitle */}
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
              className="text-xl text-gray-300 mb-10 max-w-2xl leading-relaxed"
            >
              Join Africa's premier platform for space technology education, mentorship, and career opportunities.
              Build the skills that will shape the future of space exploration across the cosmos.
            </motion.p>

            {/* Stats */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5 }}
              className="flex flex-wrap justify-center lg:justify-start gap-6 mb-10"
            >
              <div className="flex items-center space-x-3 muted-glassmorphic px-4 py-3 rounded-lg">
                <Users className="h-5 w-5 text-blue-400" />
                <span className="text-white font-medium">10,000+ Learners</span>
              </div>
              <div className="flex items-center space-x-3 muted-glassmorphic px-4 py-3 rounded-lg">
                <BookOpen className="h-5 w-5 text-pink-400" />
                <span className="text-white font-medium">200+ Courses</span>
              </div>
              <div className="flex items-center space-x-3 muted-glassmorphic px-4 py-3 rounded-lg">
                <Award className="h-5 w-5 text-purple-400" />
                <span className="text-white font-medium">500+ Certificates</span>
              </div>
            </motion.div>

            {/* CTA Buttons */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6 }}
              className="flex flex-col sm:flex-row gap-6 justify-center lg:justify-start"
            >
              <Link
                href="/auth/signup"
                className="bg-gradient-to-r from-blue-600 to-pink-600 text-white px-10 py-4 rounded-lg hover:from-pink-600 hover:to-purple-600 transition-all duration-300 flex items-center justify-center space-x-3 group font-semibold text-lg"
              >
                <Zap className="h-6 w-6" />
                <span>Start Learning</span>
                <ArrowRight className="h-6 w-6 group-hover:translate-x-1 transition-transform" />
              </Link>
              <button className="muted-glassmorphic text-white px-10 py-4 rounded-lg hover:bg-white/3 transition-all duration-300 flex items-center justify-center space-x-3 group font-semibold text-lg">
                <Play className="h-6 w-6" />
                <span>Watch Demo</span>
              </button>
            </motion.div>
          </motion.div>

          {/* Right Column - Cosmic Visual */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="relative"
          >
            {/* Main Cosmic Visual Container */}
            <div className="relative muted-glassmorphic rounded-3xl p-8 overflow-hidden">
              {/* Muted Cosmic Background */}
              <div className="absolute inset-0 dark-blur-gradient opacity-30 rounded-3xl"></div>

              {/* Floating Cosmic Elements */}
              <div className="absolute -top-4 -right-4 w-20 h-20 bg-gradient-to-r from-blue-500 to-pink-600 rounded-full opacity-40 animate-float" />
              <div className="absolute -bottom-4 -left-4 w-16 h-16 bg-gradient-to-r from-purple-600 to-blue-600 rounded-full opacity-30 animate-float animation-delay-2000" />
              <div className="absolute top-1/2 right-8 w-3 h-3 bg-purple-400 rounded-full animate-twinkle animation-delay-1000" />
              <div className="absolute bottom-1/4 left-8 w-2 h-2 bg-blue-400 rounded-full animate-twinkle animation-delay-3000" />

              {/* Interactive Learning Display */}
              <div className="relative z-10 aspect-video muted-glassmorphic rounded-lg flex items-center justify-center">
                <div className="text-center text-white">
                  <div className="w-24 h-24 mx-auto mb-4 bg-gradient-to-r from-blue-600 to-pink-600 rounded-full flex items-center justify-center">
                    <svg className="w-12 h-12" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M12 2L13.09 8.26L22 9L13.09 9.74L12 16L10.91 9.74L2 9L10.91 8.26L12 2Z" />
                    </svg>
                  </div>
                  <h3 className="text-2xl font-semibold mb-2 glow-text font-orbitron">Interactive Learning</h3>
                  <p className="text-gray-300">Experience space technology through hands-on projects</p>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
