import { NextRequest, NextResponse } from 'next/server';
import { connectDB } from '@/lib/mongodb';
import { User } from '@/models/User';
import { requireAdmin } from '@/lib/auth-middleware';
import mongoose from 'mongoose';

// Import the RoleApplication model from the previous file
const RoleApplicationSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  currentRole: {
    type: String,
    enum: ['student', 'mentor', 'employer', 'partner', 'admin'],
    required: true,
  },
  requestedRole: {
    type: String,
    enum: ['mentor', 'employer', 'partner'],
    required: true,
  },
  status: {
    type: String,
    enum: ['pending', 'approved', 'rejected', 'withdrawn'],
    default: 'pending',
  },
  applicationData: {
    reason: { type: String, required: true },
    experience: String,
    qualifications: [String],
    portfolio: String,
    references: [{
      name: String,
      email: String,
      relationship: String,
    }],
    companyInfo: {
      name: String,
      website: String,
      size: String,
      industry: String,
      verificationDocuments: [String],
    },
    partnershipInfo: {
      organizationName: String,
      organizationType: String,
      region: String,
      website: String,
      verificationDocuments: [String],
    },
  },
  reviewedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
  },
  reviewedAt: Date,
  reviewNotes: String,
  appliedAt: {
    type: Date,
    default: Date.now,
  },
}, {
  timestamps: true,
});

const RoleApplication = mongoose.models.RoleApplication || 
  mongoose.model('RoleApplication', RoleApplicationSchema);

export async function GET(request: NextRequest) {
  return requireAdmin(request, async (req) => {
    try {
      await connectDB();

      const { searchParams } = new URL(request.url);
      const status = searchParams.get('status') || 'pending';
      const requestedRole = searchParams.get('requestedRole');
      const page = parseInt(searchParams.get('page') || '1');
      const limit = parseInt(searchParams.get('limit') || '20');
      const skip = (page - 1) * limit;

      // Build query
      const query: any = {};
      
      if (status !== 'all') {
        query.status = status;
      }
      
      if (requestedRole) {
        query.requestedRole = requestedRole;
      }

      const applications = await RoleApplication.find(query)
        .populate('userId', 'name email profile avatar createdAt')
        .populate('reviewedBy', 'name email')
        .sort({ appliedAt: -1 })
        .skip(skip)
        .limit(limit);

      const total = await RoleApplication.countDocuments(query);

      // Get statistics
      const stats = await RoleApplication.aggregate([
        {
          $group: {
            _id: '$status',
            count: { $sum: 1 },
          },
        },
      ]);

      const roleStats = await RoleApplication.aggregate([
        {
          $group: {
            _id: '$requestedRole',
            count: { $sum: 1 },
          },
        },
      ]);

      return NextResponse.json({
        success: true,
        data: {
          applications,
          pagination: {
            page,
            limit,
            total,
            pages: Math.ceil(total / limit),
          },
          statistics: {
            byStatus: stats.reduce((acc, stat) => {
              acc[stat._id] = stat.count;
              return acc;
            }, {}),
            byRole: roleStats.reduce((acc, stat) => {
              acc[stat._id] = stat.count;
              return acc;
            }, {}),
          },
        },
      });
    } catch (error) {
      console.error('Error fetching role applications:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to fetch role applications' },
        { status: 500 }
      );
    }
  });
}
