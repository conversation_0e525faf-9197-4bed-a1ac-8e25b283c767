'use client';

import React, { useState, useEffect } from 'react';
import { UserRole } from '@/lib/permissions';
import { RoleBadge } from '@/components/auth/RoleGuard';

interface User {
  _id: string;
  name: string;
  email: string;
  role: UserRole;
  isActive: boolean;
  createdAt: string;
  roleHistory?: {
    previousRole: string;
    newRole: string;
    changedAt: string;
    reason: string;
  }[];
}

interface RoleApplication {
  _id: string;
  userId: {
    _id: string;
    name: string;
    email: string;
  };
  currentRole: UserRole;
  requestedRole: UserRole;
  status: 'pending' | 'approved' | 'rejected';
  applicationData: {
    reason: string;
    experience?: string;
  };
  appliedAt: string;
}

export default function RoleManagement() {
  const [users, setUsers] = useState<User[]>([]);
  const [applications, setApplications] = useState<RoleApplication[]>([]);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'users' | 'applications'>('applications');

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      const [usersRes, applicationsRes] = await Promise.all([
        fetch('/api/admin/users'),
        fetch('/api/admin/role-applications'),
      ]);

      if (usersRes.ok) {
        const usersData = await usersRes.json();
        setUsers(usersData.data.users || []);
      }

      if (applicationsRes.ok) {
        const applicationsData = await applicationsRes.json();
        setApplications(applicationsData.data.applications || []);
      }
    } catch (error) {
      console.error('Error fetching data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleRoleChange = async (userId: string, newRole: UserRole, reason: string) => {
    try {
      const response = await fetch(`/api/admin/users/${userId}/role`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ role: newRole, reason }),
      });

      if (response.ok) {
        await fetchData();
        setSelectedUser(null);
      } else {
        const error = await response.json();
        alert(`Error: ${error.error}`);
      }
    } catch (error) {
      console.error('Error updating role:', error);
      alert('Failed to update role');
    }
  };

  const handleApplicationReview = async (applicationId: string, action: 'approve' | 'reject', notes: string) => {
    try {
      const response = await fetch(`/api/admin/role-applications/${applicationId}/review`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action, reviewNotes: notes }),
      });

      if (response.ok) {
        await fetchData();
      } else {
        const error = await response.json();
        alert(`Error: ${error.error}`);
      }
    } catch (error) {
      console.error('Error reviewing application:', error);
      alert('Failed to review application');
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto p-6">
      <h1 className="text-3xl font-bold text-gray-900 mb-6">Role Management</h1>

      {/* Tabs */}
      <div className="border-b border-gray-200 mb-6">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setActiveTab('applications')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'applications'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Role Applications ({applications.filter(app => app.status === 'pending').length})
          </button>
          <button
            onClick={() => setActiveTab('users')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'users'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            All Users ({users.length})
          </button>
        </nav>
      </div>

      {/* Role Applications Tab */}
      {activeTab === 'applications' && (
        <div className="space-y-6">
          <h2 className="text-xl font-semibold text-gray-900">Pending Role Applications</h2>
          
          {applications.filter(app => app.status === 'pending').length === 0 ? (
            <div className="text-center py-8">
              <p className="text-gray-500">No pending applications</p>
            </div>
          ) : (
            <div className="grid gap-6">
              {applications
                .filter(app => app.status === 'pending')
                .map((application) => (
                  <ApplicationCard
                    key={application._id}
                    application={application}
                    onReview={handleApplicationReview}
                  />
                ))}
            </div>
          )}
        </div>
      )}

      {/* Users Tab */}
      {activeTab === 'users' && (
        <div className="space-y-6">
          <h2 className="text-xl font-semibold text-gray-900">User Management</h2>
          
          <div className="bg-white shadow overflow-hidden sm:rounded-md">
            <ul className="divide-y divide-gray-200">
              {users.map((user) => (
                <UserListItem
                  key={user._id}
                  user={user}
                  onRoleChange={handleRoleChange}
                />
              ))}
            </ul>
          </div>
        </div>
      )}
    </div>
  );
}

function ApplicationCard({ 
  application, 
  onReview 
}: { 
  application: RoleApplication; 
  onReview: (id: string, action: 'approve' | 'reject', notes: string) => void;
}) {
  const [reviewNotes, setReviewNotes] = useState('');
  const [isReviewing, setIsReviewing] = useState(false);

  const handleReview = async (action: 'approve' | 'reject') => {
    setIsReviewing(true);
    await onReview(application._id, action, reviewNotes);
    setIsReviewing(false);
    setReviewNotes('');
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow border">
      <div className="flex justify-between items-start mb-4">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">
            {application.userId.name}
          </h3>
          <p className="text-gray-600">{application.userId.email}</p>
          <div className="flex items-center gap-2 mt-2">
            <RoleBadge role={application.currentRole} />
            <span className="text-gray-400">→</span>
            <RoleBadge role={application.requestedRole} />
          </div>
        </div>
        <span className="text-sm text-gray-500">
          Applied {new Date(application.appliedAt).toLocaleDateString()}
        </span>
      </div>

      <div className="mb-4">
        <h4 className="font-medium text-gray-900 mb-2">Reason for Request:</h4>
        <p className="text-gray-700 bg-gray-50 p-3 rounded">{application.applicationData.reason}</p>
      </div>

      {application.applicationData.experience && (
        <div className="mb-4">
          <h4 className="font-medium text-gray-900 mb-2">Experience:</h4>
          <p className="text-gray-700 bg-gray-50 p-3 rounded">{application.applicationData.experience}</p>
        </div>
      )}

      <div className="space-y-3">
        <textarea
          value={reviewNotes}
          onChange={(e) => setReviewNotes(e.target.value)}
          placeholder="Add review notes (optional)..."
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          rows={3}
        />
        
        <div className="flex gap-3">
          <button
            onClick={() => handleReview('approve')}
            disabled={isReviewing}
            className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50"
          >
            Approve
          </button>
          <button
            onClick={() => handleReview('reject')}
            disabled={isReviewing}
            className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50"
          >
            Reject
          </button>
        </div>
      </div>
    </div>
  );
}

function UserListItem({ 
  user, 
  onRoleChange 
}: { 
  user: User; 
  onRoleChange: (userId: string, newRole: UserRole, reason: string) => void;
}) {
  const [isEditing, setIsEditing] = useState(false);
  const [selectedRole, setSelectedRole] = useState<UserRole>(user.role);
  const [reason, setReason] = useState('');

  const roles: UserRole[] = ['student', 'mentor', 'employer', 'partner', 'admin'];

  const handleSave = () => {
    if (selectedRole !== user.role && reason.trim()) {
      onRoleChange(user._id, selectedRole, reason);
      setIsEditing(false);
      setReason('');
    }
  };

  return (
    <li className="px-6 py-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <div className="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
              <span className="text-sm font-medium text-gray-700">
                {user.name.charAt(0).toUpperCase()}
              </span>
            </div>
          </div>
          <div className="ml-4">
            <div className="flex items-center gap-3">
              <p className="text-sm font-medium text-gray-900">{user.name}</p>
              <RoleBadge role={user.role} />
            </div>
            <p className="text-sm text-gray-500">{user.email}</p>
          </div>
        </div>

        <div className="flex items-center gap-3">
          {isEditing ? (
            <div className="flex items-center gap-2">
              <select
                value={selectedRole}
                onChange={(e) => setSelectedRole(e.target.value as UserRole)}
                className="px-3 py-1 border border-gray-300 rounded text-sm"
              >
                {roles.map((role) => (
                  <option key={role} value={role}>
                    {role.charAt(0).toUpperCase() + role.slice(1)}
                  </option>
                ))}
              </select>
              <input
                type="text"
                placeholder="Reason for change"
                value={reason}
                onChange={(e) => setReason(e.target.value)}
                className="px-3 py-1 border border-gray-300 rounded text-sm w-40"
              />
              <button
                onClick={handleSave}
                disabled={selectedRole === user.role || !reason.trim()}
                className="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700 disabled:opacity-50"
              >
                Save
              </button>
              <button
                onClick={() => {
                  setIsEditing(false);
                  setSelectedRole(user.role);
                  setReason('');
                }}
                className="px-3 py-1 bg-gray-300 text-gray-700 rounded text-sm hover:bg-gray-400"
              >
                Cancel
              </button>
            </div>
          ) : (
            <button
              onClick={() => setIsEditing(true)}
              className="px-3 py-1 bg-gray-100 text-gray-700 rounded text-sm hover:bg-gray-200"
            >
              Change Role
            </button>
          )}
        </div>
      </div>
    </li>
  );
}
